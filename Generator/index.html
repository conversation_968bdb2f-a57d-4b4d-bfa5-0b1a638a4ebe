<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>eHroby - Správa čistenia hrobov</title>

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#5e2e60">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="eHroby">
    <meta name="description" content="Aplikácia pre správu mesačného čistenia hrobov - vytvárame pokojné spomienky">

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" href="https://storage.googleapis.com/espomienka/logo36.png">
    <link rel="apple-touch-icon" sizes="152x152" href="icons/icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="icons/icon-180x180.png">

    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="icons/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icons/icon-16x16.png">

    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <!-- html2pdf.js Library - Local version for Electron with CDN fallback -->
    <script src="html2pdf.bundle.min.js"></script>
    <script>
        // Verify html2pdf is loaded with fallback to CDN
        window.addEventListener('load', function() {
            if (typeof window.html2pdf !== 'undefined') {
                console.log('html2pdf loaded successfully from local source');
            } else {
                console.warn('Local html2pdf failed to load, trying CDN fallback...');
                // Try loading from CDN as fallback
                const script = document.createElement('script');
                script.src = 'https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.3/html2pdf.bundle.min.js';
                script.onload = function() {
                    if (typeof window.html2pdf !== 'undefined') {
                        console.log('html2pdf loaded successfully from CDN fallback');
                    } else {
                        console.error('html2pdf failed to load from both local and CDN sources');
                        alert('Chyba: Nepodarilo sa načítať html2pdf knižnicu. PDF generovanie nebude fungovať.');
                    }
                };
                script.onerror = function() {
                    console.error('html2pdf failed to load from CDN');
                    alert('Chyba: Nepodarilo sa načítať html2pdf knižnicu. PDF generovanie nebude fungovať.');
                };
                document.head.appendChild(script);
            }
        });
    </script>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Offline Indicator -->
    <div id="offline-indicator" class="offline-indicator">
        <i class="fas fa-wifi"></i> Aplikácia pracuje v offline režime
    </div>

    <!-- PWA Splash Screen -->
    <div id="pwa-splash" class="pwa-splash" style="display: none;">
        <img src="https://storage.googleapis.com/espomienka/logo36.png" alt="eHroby" class="pwa-splash-logo">
        <h1 class="pwa-splash-title">eHroby</h1>
        <p class="pwa-splash-subtitle">Vytvárame pokojné spomienky</p>
    </div>

    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo-section">
                    <img src="https://storage.googleapis.com/espomienka/logo36.png" alt="eSpomienka Logo" class="logo-image">
                    <div class="logo-text">
                        <h1>eSpomienka</h1>
                        <span class="subtitle">Starostlivosť o hrobové miesta</span>
                    </div>
                </div>

                <!-- Main Navigation - Hidden on mobile, shown on desktop -->
                <nav class="main-nav desktop-nav">
                    <button class="nav-tab" data-tab="dashboard">
                        <i class="fas fa-home"></i> Dashboard
                    </button>
                    <button class="nav-tab" data-tab="quotes">
                        <i class="fas fa-file-invoice"></i> Cenová ponuka
                    </button>
                    <button class="nav-tab" data-tab="clients">
                        <i class="fas fa-users"></i> Klienti
                    </button>
                    <button class="nav-tab" data-tab="orders">
                        <i class="fas fa-tasks"></i> Objednávky
                    </button>
                    <button class="nav-tab" data-tab="invoices">
                        <i class="fas fa-file-invoice-dollar"></i> Faktúry
                    </button>
                    <button class="nav-tab" data-tab="services">
                        <i class="fas fa-cogs"></i> Služby
                    </button>
                    <button class="nav-tab" data-tab="settings">
                        <i class="fas fa-cog"></i> Nastavenia
                    </button>
                    <button class="nav-tab" data-tab="email">
                        <i class="fas fa-envelope"></i> Email
                    </button>
                </nav>

                <!-- Mobile Navigation Header -->
                <div class="mobile-nav-header">
                    <button class="back-button" onclick="goToDashboard()" style="display: none;">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <span class="current-section-title">Dashboard</span>
                    <button class="menu-button" onclick="toggleMobileMenu()">
                        <div class="hamburger"></div>
                    </button>
                </div>

                <div class="header-actions">
                    <!-- Header actions removed as requested -->
                </div>
            </div>
        </header>

        <div class="main-content">
            <!-- Tab Content Container -->
            <div class="tab-content">

                <!-- DASHBOARD TAB - NEW MAIN LANDING PAGE -->
                <div id="dashboard-tab" class="tab-pane active">
                    <div class="dashboard-container">
                        <!-- Welcome Section -->
                        <div class="welcome-section">
                            <h1 class="dashboard-title">
                                <img src="https://storage.googleapis.com/espomienka/logo36.png" alt="eHroby" class="dashboard-logo">
                                Vitajte v eHroby
                            </h1>
                            <p class="dashboard-subtitle">Vytvárame pokojné spomienky</p>
                        </div>

                        <!-- Main Navigation Grid -->
                        <div class="dashboard-grid">
                            <button class="dashboard-button pricing-button" data-tab="quotes">
                                <div class="button-icon">💰</div>
                                <div class="button-content">
                                    <h3>Cenová ponuka</h3>
                                    <p>Vytvorenie cenových ponúk pre služby</p>
                                </div>
                            </button>

                            <button class="dashboard-button invoices-button" data-tab="invoices">
                                <div class="button-icon">📄</div>
                                <div class="button-content">
                                    <h3>Faktúry</h3>
                                    <p>Správa a generovanie faktúr</p>
                                </div>
                            </button>

                            <button class="dashboard-button orders-button" data-tab="orders">
                                <div class="button-icon">📋</div>
                                <div class="button-content">
                                    <h3>Objednávky</h3>
                                    <p>Správa objednávok a termínov</p>
                                </div>
                            </button>

                            <button class="dashboard-button settings-button" data-tab="settings">
                                <div class="button-icon">⚙️</div>
                                <div class="button-content">
                                    <h3>Nastavenia</h3>
                                    <p>Konfigurácia aplikácie</p>
                                </div>
                            </button>

                            <button class="dashboard-button email-button" data-tab="email">
                                <div class="button-icon">📧</div>
                                <div class="button-content">
                                    <h3>Email</h3>
                                    <p>Emailová komunikácia s klientmi</p>
                                </div>
                            </button>

                            <button class="dashboard-button stats-button" data-tab="clients">
                                <div class="button-icon">📊</div>
                                <div class="button-content">
                                    <h3>Štatistiky</h3>
                                    <p>Prehľad výkonnosti a klientov</p>
                                </div>
                            </button>
                        </div>

                        <!-- Quick Stats Section -->
                        <div class="quick-stats">
                            <div class="stat-card" data-loading>
                                <div class="stat-icon">👥</div>
                                <div class="stat-info">
                                    <span class="stat-number" id="totalClientsCount" data-target="0">0</span>
                                    <span class="stat-label">Klienti</span>
                                </div>
                            </div>
                            <div class="stat-card" data-loading>
                                <div class="stat-icon">📄</div>
                                <div class="stat-info">
                                    <span class="stat-number" id="totalInvoicesCount" data-target="0">0</span>
                                    <span class="stat-label">Faktúry</span>
                                </div>
                            </div>
                            <div class="stat-card" data-loading>
                                <div class="stat-icon">💰</div>
                                <div class="stat-info">
                                    <span class="stat-number" id="totalRevenueCount" data-target="0">0€</span>
                                    <span class="stat-label">Tržby</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- EMAIL TAB - NEW SECTION -->
                <div id="email-tab" class="tab-pane">
                    <div class="email-layout">
                        <div class="email-header">
                            <h2><i class="fas fa-envelope"></i> Email centrum</h2>
                            <button class="btn btn-primary" onclick="composeEmail()">
                                <i class="fas fa-plus"></i> Nový email
                            </button>
                        </div>

                        <div class="email-content">
                            <div class="email-templates">
                                <h3>Šablóny emailov</h3>
                                <div class="template-grid">
                                    <div class="template-card" onclick="useTemplate('quote')">
                                        <i class="fas fa-file-invoice"></i>
                                        <h4>Cenová ponuka</h4>
                                        <p>Odoslanie cenovej ponuky klientovi</p>
                                    </div>
                                    <div class="template-card" onclick="useTemplate('invoice')">
                                        <i class="fas fa-file-invoice-dollar"></i>
                                        <h4>Faktúra</h4>
                                        <p>Odoslanie faktúry klientovi</p>
                                    </div>
                                    <div class="template-card" onclick="useTemplate('reminder')">
                                        <i class="fas fa-bell"></i>
                                        <h4>Pripomienka</h4>
                                        <p>Pripomienka platby alebo termínu</p>
                                    </div>
                                    <div class="template-card" onclick="useTemplate('thank-you')">
                                        <i class="fas fa-heart"></i>
                                        <h4>Poďakovanie</h4>
                                        <p>Poďakovanie za spoluprácu</p>
                                    </div>
                                </div>
                            </div>

                            <div class="email-history">
                                <h3>História emailov</h3>
                                <div id="emailHistoryList" class="email-list">
                                    <p class="no-emails">Žiadne odoslané emaily</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- QUOTES TAB -->
                <div id="quotes-tab" class="tab-pane active">
                    <div class="quotes-layout">
                        <!-- Sidebar -->
                        <aside class="sidebar">
                            <nav class="service-nav">
                                <h3><i class="fas fa-list"></i> Kategórie služieb</h3>
                                <ul>
                                    <li><a href="#basic-services" class="nav-link active">
                                        <i class="fas fa-tools"></i> Základné služby
                                    </a></li>
                                    <li><a href="#packages" class="nav-link">
                                        <i class="fas fa-box"></i> Balíky služieb
                                    </a></li>
                                    <li><a href="#digital" class="nav-link">
                                        <i class="fas fa-qrcode"></i> Digitálne služby
                                    </a></li>
                                    <li><a href="#additional" class="nav-link">
                                        <i class="fas fa-plus"></i> Doplnkové služby
                                    </a></li>
                                    <li><a href="#special" class="nav-link">
                                        <i class="fas fa-star"></i> Špeciálne ponuky
                                    </a></li>
                                </ul>
                            </nav>
                        </aside>

            <!-- Main Form Area -->
            <main class="form-area">
                <!-- Customer Information -->
                <section class="customer-section">
                    <h2><i class="fas fa-user"></i> Údaje o zákazníkovi</h2>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="customerName">Meno a priezvisko *</label>
                            <input type="text" id="customerName" required>
                        </div>
                        <div class="form-group">
                            <label for="customerPhone">Telefón *</label>
                            <input type="tel" id="customerPhone" required>
                        </div>
                        <div class="form-group">
                            <label for="customerEmail">Email *</label>
                            <input type="email" id="customerEmail" required>
                        </div>
                        <div class="form-group">
                            <label for="customerAddress">Adresa</label>
                            <input type="text" id="customerAddress">
                        </div>
                        <div class="form-group full-width">
                            <label for="cemetery">Cintorín/Lokalita</label>
                            <input type="text" id="cemetery">
                        </div>
                    </div>
                </section>

                <!-- Services Selection -->
                <section class="services-section">
                    <!-- Basic Services -->
                    <div id="basic-services" class="service-category active">
                        <h3><i class="fas fa-tools"></i> Základné služby</h3>
                        <div class="services-grid">
                            <div class="service-subcategory">
                                <h4>Základná údržba</h4>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="17" data-service="Základná údržba - Urnové miesto">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Urnové miesto</span>
                                        <span class="service-price">17 EUR</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="29" data-service="Základná údržba - Jednohrob">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Jednohrob</span>
                                        <span class="service-price">29 EUR</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="40" data-service="Základná údržba - Dvojhrob">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Dvojhrob</span>
                                        <span class="service-price">40 EUR</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="52" data-service="Základná údržba - Trojhrob/Hrobka">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Trojhrob/Hrobka</span>
                                        <span class="service-price">od 52 EUR</span>
                                    </label>
                                </div>
                            </div>

                            <div class="service-subcategory">
                                <h4>Hĺbkové čistenie</h4>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="58" data-service="Hĺbkové čistenie - Urnové miesto">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Urnové miesto</span>
                                        <span class="service-price">58 EUR</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="115" data-service="Hĺbkové čistenie - Jednohrob">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Jednohrob</span>
                                        <span class="service-price">115 EUR</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="161" data-service="Hĺbkové čistenie - Dvojhrob">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Dvojhrob</span>
                                        <span class="service-price">161 EUR</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="207" data-service="Hĺbkové čistenie - Trojhrob/Hrobka">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Trojhrob/Hrobka</span>
                                        <span class="service-price">od 207 EUR</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Package Services -->
                    <div id="packages" class="service-category">
                        <h3><i class="fas fa-box"></i> Balíky služieb</h3>
                        <div class="services-grid">
                            <div class="service-subcategory">
                                <h4>Balík Sviatočný</h4>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="63" data-service="Balík Sviatočný - Urnové miesto">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Urnové miesto</span>
                                        <span class="service-price">63 EUR/rok</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="104" data-service="Balík Sviatočný - Jednohrob">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Jednohrob</span>
                                        <span class="service-price">104 EUR/rok</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="150" data-service="Balík Sviatočný - Dvojhrob">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Dvojhrob</span>
                                        <span class="service-price">150 EUR/rok</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="196" data-service="Balík Sviatočný - Trojhrob/Hrobka">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Trojhrob/Hrobka</span>
                                        <span class="service-price">od 196 EUR/rok</span>
                                    </label>
                                </div>
                            </div>

                            <div class="service-subcategory">
                                <h4>Balík Celoročný Premium</h4>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="403" data-service="Balík Celoročný Premium - Jednohrob">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Jednohrob</span>
                                        <span class="service-price">403 EUR/rok</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="518" data-service="Balík Celoročný Premium - Dvojhrob">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Dvojhrob</span>
                                        <span class="service-price">518 EUR/rok</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Digital Services -->
                    <div id="digital" class="service-category">
                        <h3><i class="fas fa-qrcode"></i> Digitálne služby - ESPOMIENKA</h3>
                        <div class="services-grid">
                            <div class="service-subcategory">
                                <h4>QR kód balíčky</h4>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="35" data-service="QR kód - Základný balíček">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Základný balíček</span>
                                        <span class="service-price">35 EUR/rok</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="65" data-service="QR kód - Rozšírený balíček">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Rozšírený balíček</span>
                                        <span class="service-price">65 EUR/3 roky</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="95" data-service="QR kód - Rodinný balíček">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Rodinný balíček</span>
                                        <span class="service-price">95 EUR/5 rokov</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="0" data-service="QR kód - Riešenie na mieru">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Riešenie na mieru</span>
                                        <span class="service-price">cena na vyžiadanie</span>
                                    </label>
                                </div>
                            </div>

                            <div class="service-subcategory">
                                <h4>Montáž QR kódu</h4>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="5" data-service="Montáž QR kódu - Prilepenie">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Prilepenie</span>
                                        <span class="service-price">5 EUR</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="25" data-service="Montáž QR kódu - Vyleptanie">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Vyleptanie</span>
                                        <span class="service-price">od 25 EUR</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Services -->
                    <div id="additional" class="service-category">
                        <h3><i class="fas fa-plus"></i> Doplnkové služby</h3>
                        <div class="services-grid">
                            <div class="service-subcategory">
                                <h4>Písmo a nápisy</h4>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="1.73" data-service="Obnova farby písma" data-custom="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Obnova farby písma</span>
                                        <span class="service-price">1,73 EUR/znak</span>
                                    </label>
                                    <div class="custom-input">
                                        <label>Počet znakov:</label>
                                        <input type="number" min="1" data-multiplier="1.73" placeholder="0">
                                    </div>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="11.50" data-service="Dosekanie nového písma" data-custom="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Dosekanie nového písma</span>
                                        <span class="service-price">11,50 EUR/znak</span>
                                    </label>
                                    <div class="custom-input">
                                        <label>Počet znakov:</label>
                                        <input type="number" min="1" data-multiplier="11.50" placeholder="0">
                                    </div>
                                </div>
                            </div>

                            <div class="service-subcategory">
                                <h4>Výsadba a materiály</h4>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="11.50" data-service="Sezónna výsadba kvetov" data-custom="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Sezónna výsadba kvetov</span>
                                        <span class="service-price">cena kvetov + 11,50 EUR</span>
                                    </label>
                                    <div class="custom-input">
                                        <label>Cena kvetov (EUR):</label>
                                        <input type="number" min="0" step="0.01" data-additional="11.50" placeholder="0.00">
                                    </div>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="17.25" data-service="Dosypanie kameniva/kôry" data-custom="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Dosypanie kameniva/kôry</span>
                                        <span class="service-price">cena materiálu + 17,25 EUR</span>
                                    </label>
                                    <div class="custom-input">
                                        <label>Cena materiálu (EUR):</label>
                                        <input type="number" min="0" step="0.01" data-additional="17.25" placeholder="0.00">
                                    </div>
                                </div>
                            </div>

                            <div class="service-subcategory">
                                <h4>Ostatné služby</h4>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="23" data-service="Samostatná impregnácia - Jednohrob">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Samostatná impregnácia - Jednohrob</span>
                                        <span class="service-price">23 EUR</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="40" data-service="Samostatná impregnácia - Dvojhrob">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Samostatná impregnácia - Dvojhrob</span>
                                        <span class="service-price">40 EUR</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="11.50" data-service="Donáška a aranžovanie" data-custom="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Donáška a aranžovanie</span>
                                        <span class="service-price">cena tovaru + 11,50 EUR</span>
                                    </label>
                                    <div class="custom-input">
                                        <label>Cena tovaru (EUR):</label>
                                        <input type="number" min="0" step="0.01" data-additional="11.50" placeholder="0.00">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Special Offers -->
                    <div id="special" class="service-category">
                        <h3><i class="fas fa-star"></i> Špeciálne ponuky</h3>
                        <div class="services-grid">
                            <div class="service-subcategory">
                                <h4>Akcia 2+1 ZADARMO - Balík Sviatočný - URNOVÉ MIESTA</h4>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="90" data-service="Akcia 2+1 ZADARMO - Balík Sviatočný Malé urnové miesto" data-discount="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Malé urnové miesto (3 roky)</span>
                                        <span class="service-price">90 EUR (normálne 135 EUR)</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="126" data-service="Akcia 2+1 ZADARMO - Balík Sviatočný Veľké urnové miesto" data-discount="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Veľké urnové miesto (3 roky)</span>
                                        <span class="service-price">126 EUR (normálne 189 EUR)</span>
                                    </label>
                                </div>
                            </div>

                            <div class="service-subcategory">
                                <h4>Akcia 2+1 ZADARMO - Balík Sviatočný - HROBY</h4>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="126" data-service="Akcia 2+1 ZADARMO - Balík Sviatočný Urnové miesto" data-discount="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Urnové miesto (3 roky)</span>
                                        <span class="service-price">126 EUR (ušetríte 63 EUR)</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="208" data-service="Akcia 2+1 ZADARMO - Balík Sviatočný Jednohrob" data-discount="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Jednohrob (3 roky)</span>
                                        <span class="service-price">208 EUR (ušetríte 104 EUR)</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="300" data-service="Akcia 2+1 ZADARMO - Balík Sviatočný Dvojhrob" data-discount="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Dvojhrob (3 roky)</span>
                                        <span class="service-price">300 EUR (ušetríte 150 EUR)</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="392" data-service="Akcia 2+1 ZADARMO - Balík Sviatočný Trojhrob/Hrobka" data-discount="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Trojhrob/Hrobka (3 roky)</span>
                                        <span class="service-price">od 392 EUR (ušetríte 196 EUR)</span>
                                    </label>
                                </div>
                            </div>

                            <div class="service-subcategory">
                                <h4>Akcia 2+1 ZADARMO - Balík Celoročný Premium - URNOVÉ MIESTA</h4>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="354" data-service="Akcia 2+1 ZADARMO - Balík Celoročný Premium Malé urnové miesto" data-discount="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Malé urnové miesto (3 roky)</span>
                                        <span class="service-price">354 EUR (normálne 531 EUR)</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="490" data-service="Akcia 2+1 ZADARMO - Balík Celoročný Premium Veľké urnové miesto" data-discount="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Veľké urnové miesto (3 roky)</span>
                                        <span class="service-price">490 EUR (normálne 735 EUR)</span>
                                    </label>
                                </div>
                            </div>

                            <div class="service-subcategory">
                                <h4>Akcia 2+1 ZADARMO - Balík Celoročný Premium - HROBY</h4>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="806" data-service="Akcia 2+1 ZADARMO - Balík Celoročný Premium Jednohrob" data-discount="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Jednohrob (3 roky)</span>
                                        <span class="service-price">806 EUR (ušetríte 403 EUR)</span>
                                    </label>
                                </div>
                                <div class="service-item">
                                    <label class="service-label">
                                        <input type="checkbox" data-price="1036" data-service="Akcia 2+1 ZADARMO - Balík Celoročný Premium Dvojhrob" data-discount="true">
                                        <span class="checkmark"></span>
                                        <span class="service-name">Dvojhrob (3 roky)</span>
                                        <span class="service-price">1036 EUR (ušetríte 518 EUR)</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </main>

                        <!-- Calculator Sidebar -->
                        <aside class="calculator">
                            <div class="calculator-content">
                                <h3><i class="fas fa-calculator"></i> Kalkulácia</h3>
                                <div class="selected-services">
                                    <h4>Vybrané služby:</h4>
                                    <div id="selectedServicesList" class="services-list">
                                        <p class="no-services">Žiadne služby nie sú vybrané</p>
                                    </div>
                                </div>
                                <div class="discount-section">
                                    <h4>Zľava:</h4>
                                    <div class="discount-options">
                                        <label class="discount-option">
                                            <input type="radio" name="discount" value="0" checked>
                                            <span>Bez zľavy</span>
                                        </label>
                                        <label class="discount-option">
                                            <input type="radio" name="discount" value="5">
                                            <span>5%</span>
                                        </label>
                                        <label class="discount-option">
                                            <input type="radio" name="discount" value="10">
                                            <span>10%</span>
                                        </label>
                                        <label class="discount-option">
                                            <input type="radio" name="discount" value="15">
                                            <span>15%</span>
                                        </label>
                                    </div>
                                </div>
                                <div class="price-summary">
                                    <div class="price-row">
                                        <span>Súčet bez DPH:</span>
                                        <span id="subtotal">0,00 EUR</span>
                                    </div>
                                    <div class="price-row discount-row" id="discountRow" style="display: none;">
                                        <span>Zľava (<span id="discountPercent">0</span>%):</span>
                                        <span id="discountAmount">-0,00 EUR</span>
                                    </div>
                                    <div class="price-row">
                                        <span>Súčet po zľave:</span>
                                        <span id="subtotalAfterDiscount">0,00 EUR</span>
                                    </div>
                                    <div class="price-row">
                                        <span>DPH 20%:</span>
                                        <span id="vat">0,00 EUR</span>
                                    </div>
                                    <div class="price-row total">
                                        <span>Celkom s DPH:</span>
                                        <span id="total">0,00 EUR</span>
                                    </div>
                                </div>
                                <button class="btn btn-primary btn-generate" onclick="generatePDF()">
                                    <i class="fas fa-file-pdf"></i> Generovať PDF ponuku
                                </button>
                                <button class="btn btn-primary" onclick="createOrderFromQuote()" style="margin-top: 10px;">
                                    <i class="fas fa-plus"></i> Vytvoriť objednávku
                                </button>
                            </div>
                        </aside>
                    </div>
                </div>

                <!-- CLIENTS TAB -->
                <div id="clients-tab" class="tab-pane">
                    <div class="clients-layout">
                        <!-- Clients Header -->
                        <div class="clients-header">
                            <h2><i class="fas fa-users"></i> Správa klientov</h2>
                            <div class="clients-actions">
                                <button class="btn btn-primary" onclick="showAddClientModal()">
                                    <i class="fas fa-plus"></i> Nový klient
                                </button>
                                <button class="btn btn-secondary" onclick="exportClients()">
                                    <i class="fas fa-download"></i> Export CSV
                                </button>
                            </div>
                        </div>

                        <!-- Clients Stats -->
                        <div class="clients-stats">
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-users"></i></div>
                                <div class="stat-content">
                                    <h3 id="totalClients">0</h3>
                                    <p>Celkom klientov</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-euro-sign"></i></div>
                                <div class="stat-content">
                                    <h3 id="totalClientRevenue">0,00 €</h3>
                                    <p>Celkové tržby</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-file-contract"></i></div>
                                <div class="stat-content">
                                    <h3 id="activeClientContracts">0</h3>
                                    <p>Aktívne zmluvy</p>
                                </div>
                            </div>
                        </div>

                        <!-- Clients Search and Filter -->
                        <div class="clients-filters">
                            <div class="filter-group">
                                <label>Hľadať:</label>
                                <input type="text" id="clientSearch" placeholder="Meno, telefón, email...">
                            </div>
                            <div class="filter-group">
                                <label>Zoradiť podľa:</label>
                                <select id="clientSort">
                                    <option value="name">Meno</option>
                                    <option value="revenue">Tržby</option>
                                    <option value="created">Dátum vytvorenia</option>
                                </select>
                            </div>
                        </div>

                        <!-- Clients List -->
                        <div class="clients-list" id="clientsList">
                            <!-- Clients will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- ORDERS TAB - SIMPLIFIED GRAVE CLEANING SYSTEM -->
                <div id="orders-tab" class="tab-pane">
                    <!-- Simplified Navigation -->
                    <div class="cleaning-navigation">
                        <div class="cleaning-nav-sidebar">
                            <button class="cleaning-nav-tab active" data-cleaning-view="dashboard">
                                <i class="fas fa-home"></i> Dashboard
                            </button>
                            <button class="cleaning-nav-tab" data-cleaning-view="today">
                                <i class="fas fa-clipboard-list"></i> Dnešné práce
                            </button>
                            <button class="cleaning-nav-tab" data-cleaning-view="schedule">
                                <i class="fas fa-calendar-alt"></i> Harmonogram
                            </button>
                            <button class="cleaning-nav-tab" data-cleaning-view="customers">
                                <i class="fas fa-users"></i> Zákazníci
                            </button>
                            <button class="cleaning-nav-tab" data-cleaning-view="graves">
                                <i class="fas fa-monument"></i> Hroby
                            </button>
                            <button class="cleaning-nav-tab" data-cleaning-view="payments">
                                <i class="fas fa-euro-sign"></i> Platby
                            </button>
                        </div>

                        <!-- Quick Actions -->
                        <div class="cleaning-quick-actions">
                            <button class="btn btn-primary" onclick="showAddCustomerModal()">
                                <i class="fas fa-plus"></i> Pridaj zákazníka
                            </button>
                        </div>
                    </div>

                    <!-- CLEANING DASHBOARD VIEW -->
                    <div id="cleaning-dashboard" class="cleaning-view active">
                        <div class="cleaning-dashboard-layout">
                            <!-- Monthly Overview -->
                            <div class="dashboard-section">
                                <h2><i class="fas fa-chart-bar"></i> Mesačný prehľad</h2>
                                <div class="monthly-overview-card">
                                    <div class="overview-stats">
                                        <div class="stat-item">
                                            <span class="stat-label">Aktívni zákazníci:</span>
                                            <span class="stat-value" id="activeCustomers">32</span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-label">Dokončené tento mes.:</span>
                                            <span class="stat-value" id="completedThisMonth">28/32</span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-label">Mesačný príjem:</span>
                                            <span class="stat-value" id="monthlyIncome">1,280€</span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-label">Nezaplatené:</span>
                                            <span class="stat-value unpaid" id="unpaidCustomers">4 zákazníci</span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-label">Dnes na čistenie:</span>
                                            <span class="stat-value" id="todayCleanings">3</span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-label">Zajtra:</span>
                                            <span class="stat-value" id="tomorrowCleanings">2</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Today's Tasks -->
                            <div class="dashboard-section">
                                <h3><i class="fas fa-clipboard-list"></i> Dnes (15.01.2025)</h3>
                                <div class="today-tasks-card">
                                    <div id="todayTasksList" class="today-tasks-list">
                                        <!-- Populated by JavaScript -->
                                    </div>
                                    <div class="task-actions">
                                        <button class="btn btn-secondary" onclick="capturePhoto()">
                                            <i class="fas fa-camera"></i> Pridaj fotku
                                        </button>
                                        <button class="btn btn-success" onclick="completeTask()">
                                            <i class="fas fa-check"></i> Dokončiť
                                        </button>
                                        <button class="btn btn-info" onclick="addNote()">
                                            <i class="fas fa-sticky-note"></i> Poznámka
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Weekly Overview -->
                            <div class="dashboard-section">
                                <h3><i class="fas fa-calendar-week"></i> Tento týždeň</h3>
                                <div class="weekly-overview-card">
                                    <div class="week-days">
                                        <div class="week-day">
                                            <span class="day-label">Po 13.1:</span>
                                            <span class="day-count">2 hroby</span>
                                        </div>
                                        <div class="week-day">
                                            <span class="day-label">Út 14.1:</span>
                                            <span class="day-count">3 hroby</span>
                                        </div>
                                        <div class="week-day current">
                                            <span class="day-label">St 15.1:</span>
                                            <span class="day-count">3 hroby</span>
                                        </div>
                                        <div class="week-day">
                                            <span class="day-label">Št 16.1:</span>
                                            <span class="day-count">1 hrob</span>
                                        </div>
                                        <div class="week-day">
                                            <span class="day-label">Pi 17.1:</span>
                                            <span class="day-count">2 hroby</span>
                                        </div>
                                        <div class="week-day">
                                            <span class="day-label">So 18.1:</span>
                                            <span class="day-count">0 hrobov</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- TODAY'S WORK VIEW -->
                    <div id="cleaning-today" class="cleaning-view">
                        <div class="today-layout">
                            <!-- Today's Header -->
                            <div class="today-header">
                                <h2><i class="fas fa-clipboard-list"></i> Úlohy na dnes</h2>
                                <div class="today-actions">
                                    <button class="btn btn-primary" onclick="showAddTaskModal()">
                                        <i class="fas fa-plus"></i> Pridaj novú úlohu
                                    </button>
                                </div>
                            </div>

                            <!-- Today's Tasks by Cemetery -->
                            <div class="today-tasks-container">
                                <div class="cemetery-group">
                                    <h3><i class="fas fa-monument"></i> Hlavný cintorín</h3>
                                    <div class="cemetery-tasks">
                                        <div class="task-item completed">
                                            <div class="task-status">✅</div>
                                            <div class="task-info">
                                                <span class="task-grave">A-123 (M. Nováková)</span>
                                                <span class="task-time">Dokončené</span>
                                            </div>
                                        </div>
                                        <div class="task-item in-progress">
                                            <div class="task-status">⏳</div>
                                            <div class="task-info">
                                                <span class="task-grave">B-456 (J. Kováč)</span>
                                                <span class="task-time">V riešení</span>
                                            </div>
                                        </div>
                                        <div class="task-item pending">
                                            <div class="task-status">📅</div>
                                            <div class="task-info">
                                                <span class="task-grave">C-789 (P. Svoboda)</span>
                                                <span class="task-time">Čaká</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="cemetery-group">
                                    <h3><i class="fas fa-monument"></i> Petržalský cintorín</h3>
                                    <div class="cemetery-tasks">
                                        <div class="task-item pending">
                                            <div class="task-status">📅</div>
                                            <div class="task-info">
                                                <span class="task-grave">D-012 (A. Krásna)</span>
                                                <span class="task-time">Čaká</span>
                                            </div>
                                        </div>
                                        <div class="task-item pending">
                                            <div class="task-status">📅</div>
                                            <div class="task-info">
                                                <span class="task-grave">E-345 (L. Malý)</span>
                                                <span class="task-time">Čaká</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>

                    <!-- SCHEDULE VIEW -->
                    <div id="cleaning-schedule" class="cleaning-view">
                        <div class="schedule-layout">
                            <!-- Schedule Header -->
                            <div class="schedule-header">
                                <h2><i class="fas fa-calendar-alt"></i> Harmonogram</h2>
                                <div class="schedule-controls">
                                    <button class="btn btn-secondary" onclick="previousMonth()">
                                        <i class="fas fa-chevron-left"></i>
                                    </button>
                                    <h3 id="currentMonthTitle">Január 2025</h3>
                                    <button class="btn btn-secondary" onclick="nextMonth()">
                                        <i class="fas fa-chevron-right"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Monthly Calendar -->
                            <div class="monthly-calendar">
                                <div class="calendar-weekdays">
                                    <div class="weekday">Po</div>
                                    <div class="weekday">Út</div>
                                    <div class="weekday">St</div>
                                    <div class="weekday">Št</div>
                                    <div class="weekday">Pi</div>
                                    <div class="weekday">So</div>
                                    <div class="weekday">Ne</div>
                                </div>
                                <div class="calendar-days" id="calendarDaysGrid">
                                    <!-- Calendar days populated by JavaScript -->
                                </div>
                            </div>

                            <!-- Weekly Detail -->
                            <div class="weekly-detail">
                                <h3><i class="fas fa-calendar-week"></i> Týždeň 13.1 - 19.1.2025</h3>
                                <div class="week-schedule">
                                    <div class="day-schedule">
                                        <h4>Pondelok 13.1</h4>
                                        <div class="day-tasks">
                                            <div class="scheduled-task completed">
                                                <span class="task-time">09:00</span>
                                                <span class="task-customer">M. Nováková (A-123)</span>
                                                <span class="task-status">✅</span>
                                            </div>
                                            <div class="scheduled-task in-progress">
                                                <span class="task-time">10:30</span>
                                                <span class="task-customer">J. Kováč (B-456)</span>
                                                <span class="task-status">⏳</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="day-schedule">
                                        <h4>Utorok 14.1</h4>
                                        <div class="day-tasks">
                                            <div class="scheduled-task pending">
                                                <span class="task-time">09:00</span>
                                                <span class="task-customer">P. Svoboda (C-789)</span>
                                                <span class="task-status">📅</span>
                                            </div>
                                            <div class="scheduled-task pending">
                                                <span class="task-time">10:30</span>
                                                <span class="task-customer">A. Krásna (D-012)</span>
                                                <span class="task-status">📅</span>
                                            </div>
                                            <div class="scheduled-task pending">
                                                <span class="task-time">14:00</span>
                                                <span class="task-customer">L. Malý (E-345)</span>
                                                <span class="task-status">📅</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- CUSTOMERS VIEW -->
                    <div id="cleaning-customers" class="cleaning-view">
                        <div class="customers-layout">
                            <!-- Customers Header -->
                            <div class="customers-header">
                                <h2><i class="fas fa-users"></i> Zákazníci (32 aktívnych)</h2>
                                <div class="customers-actions">
                                    <button class="btn btn-primary" onclick="showAddCustomerModal()">
                                        <i class="fas fa-user-plus"></i> Pridaj zákazníka
                                    </button>
                                </div>
                            </div>

                            <!-- Customers Filters -->
                            <div class="customers-filters">
                                <div class="filter-group">
                                    <label>🔍 Vyhľadaj...</label>
                                    <input type="text" id="customersSearch" placeholder="Meno, telefón, email...">
                                </div>
                                <div class="filter-group">
                                    <label>Filter:</label>
                                    <select id="customersFilter">
                                        <option value="all">Všetci</option>
                                        <option value="monthly">🔄 Mesačne</option>
                                        <option value="unpaid">❌ Nezaplatené</option>
                                        <option value="active">✅ Aktívni</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Customers List -->
                            <div class="customers-list">
                                <div class="customer-card">
                                    <div class="customer-info">
                                        <div class="customer-name">👤 Mária Nováková</div>
                                        <div class="customer-contact">
                                            <span>📞 0903 123 456</span>
                                            <span>📧 <EMAIL></span>
                                        </div>
                                        <div class="customer-details">
                                            <span>🏛️ A-123</span>
                                            <span>💰 40€/mes</span>
                                            <span class="paid">✅ Zaplatené</span>
                                        </div>
                                        <div class="customer-schedule">
                                            <span>📅 Posledné: 15.12</span>
                                            <span>📅 Ďalšie: 15.01</span>
                                        </div>
                                    </div>
                                    <div class="customer-actions">
                                        <button class="btn btn-sm btn-secondary" onclick="viewCustomerDetail('1')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-info" onclick="callCustomer('0903123456')">
                                            <i class="fas fa-phone"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="customer-card">
                                    <div class="customer-info">
                                        <div class="customer-name">👤 Ján Kováč</div>
                                        <div class="customer-contact">
                                            <span>📞 0903 456 789</span>
                                            <span>📧 <EMAIL></span>
                                        </div>
                                        <div class="customer-details">
                                            <span>🏛️ B-456</span>
                                            <span>💰 40€/mes</span>
                                            <span class="unpaid">❌ Nezaplatené</span>
                                        </div>
                                        <div class="customer-schedule">
                                            <span>📅 Posledné: 10.12</span>
                                            <span>📅 Ďalšie: 10.01</span>
                                        </div>
                                    </div>
                                    <div class="customer-actions">
                                        <button class="btn btn-sm btn-secondary" onclick="viewCustomerDetail('2')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-info" onclick="callCustomer('0903456789')">
                                            <i class="fas fa-phone"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>

                    <!-- GRAVES VIEW -->
                    <div id="cleaning-graves" class="cleaning-view">
                        <div class="graves-layout">
                            <!-- Graves Header -->
                            <div class="graves-header">
                                <h2><i class="fas fa-monument"></i> Hroby (32 aktívnych)</h2>
                                <div class="graves-actions">
                                    <button class="btn btn-primary" onclick="showAddGraveModal()">
                                        <i class="fas fa-plus"></i> Pridaj hrob
                                    </button>
                                </div>
                            </div>

                            <!-- Graves Filters -->
                            <div class="graves-filters">
                                <div class="filter-group">
                                    <label>🔍 Vyhľadaj...</label>
                                    <input type="text" id="gravesSearch" placeholder="Číslo hrobu, meno zákazníka...">
                                </div>
                                <div class="filter-group">
                                    <label>Cintorín:</label>
                                    <select id="cemeteryFilter">
                                        <option value="all">Všetky</option>
                                        <option value="hlavny">Hlavný cintorín</option>
                                        <option value="petrzalka">Petržalský cintorín</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Graves List by Cemetery -->
                            <div class="graves-list">
                                <div class="cemetery-section">
                                    <h3><i class="fas fa-monument"></i> Hlavný cintorín</h3>
                                    <div class="graves-group">
                                        <div class="grave-item completed">
                                            <div class="grave-info">
                                                <span class="grave-number">A-123</span>
                                                <span class="grave-customer">(M. Nováková)</span>
                                                <span class="grave-status">✅ 15.01 dokončené</span>
                                            </div>
                                            <div class="grave-actions">
                                                <button class="btn btn-sm btn-secondary" onclick="viewGraveDetail('A-123')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="grave-item waiting">
                                            <div class="grave-info">
                                                <span class="grave-number">A-124</span>
                                                <span class="grave-customer">(J. Kováč)</span>
                                                <span class="grave-status">⏳ 16.01 čaká</span>
                                            </div>
                                            <div class="grave-actions">
                                                <button class="btn btn-sm btn-secondary" onclick="viewGraveDetail('A-124')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="grave-item scheduled">
                                            <div class="grave-info">
                                                <span class="grave-number">A-125</span>
                                                <span class="grave-customer">(P. Svoboda)</span>
                                                <span class="grave-status">📅 17.01 naplánované</span>
                                            </div>
                                            <div class="grave-actions">
                                                <button class="btn btn-sm btn-secondary" onclick="viewGraveDetail('A-125')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="cemetery-section">
                                    <h3><i class="fas fa-monument"></i> Petržalský cintorín</h3>
                                    <div class="graves-group">
                                        <div class="grave-item scheduled">
                                            <div class="grave-info">
                                                <span class="grave-number">B-456</span>
                                                <span class="grave-customer">(A. Krásna)</span>
                                                <span class="grave-status">📅 18.01 naplánované</span>
                                            </div>
                                            <div class="grave-actions">
                                                <button class="btn btn-sm btn-secondary" onclick="viewGraveDetail('B-456')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="grave-item skipped">
                                            <div class="grave-info">
                                                <span class="grave-number">B-457</span>
                                                <span class="grave-customer">(L. Malý)</span>
                                                <span class="grave-status">❌ 10.01 preskočené</span>
                                            </div>
                                            <div class="grave-actions">
                                                <button class="btn btn-sm btn-secondary" onclick="viewGraveDetail('B-457')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- PAYMENTS VIEW -->
                    <div id="cleaning-payments" class="cleaning-view">
                        <div class="payments-layout">
                            <!-- Payments Header -->
                            <div class="payments-header">
                                <h2><i class="fas fa-euro-sign"></i> Platby - Január 2025</h2>
                                <div class="payments-summary">
                                    <span class="summary-item">Celkom: <strong>1,280€</strong></span>
                                    <span class="summary-item">Zaplatené: <strong>1,120€</strong></span>
                                    <span class="summary-item unpaid">Nezaplatené: <strong>160€</strong></span>
                                </div>
                            </div>

                            <!-- Payments List -->
                            <div class="payments-list">
                                <div class="payment-item paid">
                                    <div class="payment-info">
                                        <span class="payment-status">✅</span>
                                        <span class="payment-customer">M. Nováková</span>
                                        <span class="payment-amount">40€</span>
                                        <span class="payment-date">(01.01.2025)</span>
                                    </div>
                                    <div class="payment-actions">
                                        <button class="btn btn-sm btn-secondary" onclick="viewPaymentDetail('1')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="payment-item paid">
                                    <div class="payment-info">
                                        <span class="payment-status">✅</span>
                                        <span class="payment-customer">J. Kováč</span>
                                        <span class="payment-amount">40€</span>
                                        <span class="payment-date">(02.01.2025)</span>
                                    </div>
                                    <div class="payment-actions">
                                        <button class="btn btn-sm btn-secondary" onclick="viewPaymentDetail('2')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="payment-item paid">
                                    <div class="payment-info">
                                        <span class="payment-status">✅</span>
                                        <span class="payment-customer">P. Svoboda</span>
                                        <span class="payment-amount">40€</span>
                                        <span class="payment-date">(03.01.2025)</span>
                                    </div>
                                    <div class="payment-actions">
                                        <button class="btn btn-sm btn-secondary" onclick="viewPaymentDetail('3')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="payment-item overdue">
                                    <div class="payment-info">
                                        <span class="payment-status">❌</span>
                                        <span class="payment-customer">A. Krásna</span>
                                        <span class="payment-amount">40€</span>
                                        <span class="payment-date">(po termíne 7 dní)</span>
                                    </div>
                                    <div class="payment-actions">
                                        <button class="btn btn-sm btn-warning" onclick="sendReminder('4')">
                                            <i class="fas fa-envelope"></i>
                                        </button>
                                        <button class="btn btn-sm btn-info" onclick="callCustomer('4')">
                                            <i class="fas fa-phone"></i>
                                        </button>
                                        <button class="btn btn-sm btn-success" onclick="markAsPaid('4')">
                                            <i class="fas fa-credit-card"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="payment-item overdue">
                                    <div class="payment-info">
                                        <span class="payment-status">❌</span>
                                        <span class="payment-customer">L. Malý</span>
                                        <span class="payment-amount">40€</span>
                                        <span class="payment-date">(po termíne 12 dní)</span>
                                    </div>
                                    <div class="payment-actions">
                                        <button class="btn btn-sm btn-warning" onclick="sendReminder('5')">
                                            <i class="fas fa-envelope"></i>
                                        </button>
                                        <button class="btn btn-sm btn-info" onclick="callCustomer('5')">
                                            <i class="fas fa-phone"></i>
                                        </button>
                                        <button class="btn btn-sm btn-success" onclick="markAsPaid('5')">
                                            <i class="fas fa-credit-card"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>






                <!-- INVOICES TAB -->
                <div id="invoices-tab" class="tab-pane">
                    <div class="invoices-layout">
                        <!-- Invoices Header -->
                        <div class="invoices-header">
                            <h2><i class="fas fa-file-invoice-dollar"></i> Správa faktúr</h2>
                            <div class="invoices-actions">
                                <button class="btn btn-primary" onclick="showAddInvoiceModal()">
                                    <i class="fas fa-plus"></i> Nová faktúra
                                </button>
                                <button class="btn btn-secondary" onclick="showBulkInvoiceModal()">
                                    <i class="fas fa-layer-group"></i> Hromadné fakturovanie
                                </button>
                            </div>
                        </div>

                        <!-- Invoices Stats -->
                        <div class="invoices-stats">
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-file-invoice"></i></div>
                                <div class="stat-content">
                                    <h3 id="totalInvoices">0</h3>
                                    <p>Vydané faktúry</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-check-circle"></i></div>
                                <div class="stat-content">
                                    <h3 id="paidInvoices">0</h3>
                                    <p>Uhradené (93%)</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-clock"></i></div>
                                <div class="stat-content">
                                    <h3 id="pendingInvoicesCount">0</h3>
                                    <p>Čakajúce</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-euro-sign"></i></div>
                                <div class="stat-content">
                                    <h3 id="totalInvoiceAmount">0,00 €</h3>
                                    <p>Celková suma</p>
                                </div>
                            </div>
                        </div>

                        <!-- Invoices Filters -->
                        <div class="invoices-filters">
                            <div class="filter-group">
                                <label>Filter:</label>
                                <select id="invoiceStatusFilter">
                                    <option value="all">Všetky</option>
                                    <option value="pending">Čakajúce</option>
                                    <option value="paid">Uhradené</option>
                                    <option value="overdue">Po splatnosti</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label>Rok:</label>
                                <select id="invoiceYearFilter">
                                    <option value="2025">2025</option>
                                    <option value="2024">2024</option>
                                    <option value="all">Všetky</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label>Hľadať:</label>
                                <input type="text" id="invoiceSearch" placeholder="Číslo faktúry, zákazník...">
                            </div>
                        </div>

                        <!-- Invoices List -->
                        <div class="invoices-list" id="invoicesList">
                            <!-- Invoices will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- SERVICES TAB -->
                <div id="services-tab" class="tab-pane">
                    <div class="services-layout">
                        <!-- Services Header -->
                        <div class="services-header">
                            <h2><i class="fas fa-cogs"></i> Správa služieb</h2>
                            <div class="services-actions">
                                <button class="btn btn-primary" onclick="showAddServiceModal()">
                                    <i class="fas fa-plus"></i> Nová služba
                                </button>
                                <button class="btn btn-secondary" onclick="resetServicePrices()">
                                    <i class="fas fa-redo"></i> Obnoviť predvolené ceny
                                </button>
                            </div>
                        </div>

                        <!-- Services Categories -->
                        <div class="services-categories">
                            <div class="service-category-section">
                                <h3><i class="fas fa-tools"></i> Základné služby</h3>
                                <div class="services-grid" id="basicServicesGrid">
                                    <!-- Basic services will be populated by JavaScript -->
                                </div>
                            </div>

                            <div class="service-category-section">
                                <h3><i class="fas fa-box"></i> Balíky služieb</h3>
                                <div class="services-grid" id="packageServicesGrid">
                                    <!-- Package services will be populated by JavaScript -->
                                </div>
                            </div>

                            <div class="service-category-section">
                                <h3><i class="fas fa-qrcode"></i> Digitálne služby</h3>
                                <div class="services-grid" id="digitalServicesGrid">
                                    <!-- Digital services will be populated by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SETTINGS TAB -->
                <div id="settings-tab" class="tab-pane">
                    <div class="settings-layout">
                        <h2><i class="fas fa-cog"></i> Nastavenia</h2>
                        <div class="settings-content">
                            <div class="settings-section">
                                <h3>Všeobecné nastavenia</h3>
                                <div class="setting-item">
                                    <label>Predvolená lokalita:</label>
                                    <select id="defaultLocation">
                                        <option value="bratislava">Bratislava</option>
                                        <option value="petrzalka">Petržalka</option>
                                    </select>
                                </div>
                                <div class="setting-item">
                                    <label>Automatické plánovanie úloh:</label>
                                    <input type="checkbox" id="autoScheduling" checked>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>CRM a fakturácia</h3>
                                <div class="setting-item">
                                    <label>Automatické číslovanie faktúr:</label>
                                    <input type="checkbox" id="autoInvoiceNumbering" checked>
                                </div>
                                <div class="setting-item">
                                    <label>Predvolená splatnosť faktúr (dni):</label>
                                    <input type="number" id="defaultDueDays" value="2" min="1" max="30">
                                </div>
                                <div class="setting-item">
                                    <label>DPH sadzba (%):</label>
                                    <input type="number" id="vatRate" value="20" min="0" max="100" step="0.1">
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Firemné údaje</h3>
                                <div class="setting-item">
                                    <label>Telefón:</label>
                                    <input type="tel" id="companyPhone" value="+421 xxx xxx xxx">
                                </div>
                                <div class="setting-item">
                                    <label>Email:</label>
                                    <input type="email" id="companyEmail" value="<EMAIL>">
                                </div>
                                <div class="setting-item">
                                    <label>Webstránka:</label>
                                    <input type="url" id="companyWebsite" value="www.animamundi.sk">
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Štatistiky a reporty</h3>
                                <div class="settings-actions">
                                    <button class="btn btn-primary" onclick="showAdvancedStatsModal()">
                                        <i class="fas fa-chart-bar"></i> Pokročilé štatistiky
                                    </button>
                                    <button class="btn btn-secondary" onclick="exportReport('monthly')">
                                        <i class="fas fa-download"></i> Export mesačný report
                                    </button>
                                    <button class="btn btn-secondary" onclick="exportReport('clients')">
                                        <i class="fas fa-download"></i> Export klienti
                                    </button>
                                    <button class="btn btn-secondary" onclick="exportReport('invoices')">
                                        <i class="fas fa-download"></i> Export faktúry
                                    </button>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Testovanie funkcií</h3>
                                <div class="settings-actions">
                                    <button class="btn btn-primary" onclick="testPDFGeneration()">
                                        <i class="fas fa-file-pdf"></i> Test PDF generovania
                                    </button>
                                    <button class="btn btn-secondary" onclick="createSampleData()">
                                        <i class="fas fa-database"></i> Vytvoriť vzorové dáta
                                    </button>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Údržba systému</h3>
                                <div class="settings-actions">
                                    <button class="btn btn-warning" onclick="clearAllData()">
                                        <i class="fas fa-trash"></i> Vymazať všetky dáta
                                    </button>
                                    <button class="btn btn-secondary" onclick="exportAllData()">
                                        <i class="fas fa-download"></i> Zálohovať dáta
                                    </button>
                                    <button class="btn btn-secondary" onclick="importData()">
                                        <i class="fas fa-upload"></i> Obnoviť dáta
                                    </button>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Notifikácie</h3>
                                <div class="setting-item">
                                    <label>Pripomienky úloh:</label>
                                    <input type="checkbox" id="taskReminders" checked>
                                </div>
                                <div class="setting-item">
                                    <label>Email notifikácie:</label>
                                    <input type="checkbox" id="emailNotifications">
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Správa dát</h3>
                                <div class="setting-item">
                                    <label>Ukážkové dáta:</label>
                                    <button class="btn btn-secondary" onclick="resetToSampleData()">
                                        <i class="fas fa-redo"></i> Obnoviť ukážkové dáta
                                    </button>
                                </div>
                                <div class="setting-item">
                                    <label>Vymazať všetko:</label>
                                    <button class="btn btn-danger" onclick="clearAllData()">
                                        <i class="fas fa-trash"></i> Vymazať všetky dáta
                                    </button>
                                </div>
                                <div class="setting-item">
                                    <label>Export dát:</label>
                                    <button class="btn btn-primary" onclick="exportData()">
                                        <i class="fas fa-download"></i> Exportovať dáta
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modals -->
        <!-- Add Order Modal -->
        <div id="addOrderModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-plus"></i> Pridať objednávku</h3>
                    <span class="close" onclick="closeModal('addOrderModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="addOrderForm">
                        <div class="form-grid">
                            <div class="form-group">
                                <label>Meno zákazníka:</label>
                                <input type="text" id="orderCustomerName" required>
                            </div>
                            <div class="form-group">
                                <label>Telefón:</label>
                                <input type="tel" id="orderCustomerPhone" required>
                            </div>
                            <div class="form-group">
                                <label>Email:</label>
                                <input type="email" id="orderCustomerEmail" required>
                            </div>
                            <div class="form-group">
                                <label>Lokalita:</label>
                                <select id="orderLocation" required>
                                    <option value="">Vyberte lokalitu</option>
                                    <option value="bratislava">Cintorín Bratislava</option>
                                    <option value="petrzalka">Cintorín Petržalka</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Typ balíka:</label>
                                <select id="orderPackage" required>
                                    <option value="">Vyberte balík</option>
                                    <option value="sviatocny_urnove">Sviatočný - Urnové miesto</option>
                                    <option value="sviatocny_jednohrob">Sviatočný - Jednohrob</option>
                                    <option value="sviatocny_dvojhrob">Sviatočný - Dvojhrob</option>
                                    <option value="celorocny_urnove">Celoročný Premium - Urnové miesto</option>
                                    <option value="celorocny_jednohrob">Celoročný Premium - Jednohrob</option>
                                    <option value="celorocny_dvojhrob">Celoročný Premium - Dvojhrob</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Dátum začiatku:</label>
                                <input type="date" id="orderStartDate" required>
                            </div>
                            <div class="form-group">
                                <label>Zostávajúce čistenia (voliteľné):</label>
                                <input type="number" id="orderRemainingCleanings" min="0" max="100" placeholder="Automaticky podľa balíka">
                                <small class="form-help">Nechajte prázdne pre automatický výpočet podľa balíka. Použite pre migráciu klientov z iného systému.</small>
                            </div>
                        </div>
                        <div class="modal-actions">
                            <button type="button" class="btn btn-secondary" onclick="closeModal('addOrderModal')">Zrušiť</button>
                            <button type="submit" class="btn btn-primary">Vytvoriť objednávku</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Calendar Modal -->
        <div id="calendarModal" class="modal">
            <div class="modal-content large">
                <div class="modal-header">
                    <h3><i class="fas fa-calendar"></i> Kalendárny pohľad</h3>
                    <span class="close" onclick="closeModal('calendarModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="calendar-container">
                        <div class="calendar-header">
                            <button class="btn btn-secondary" onclick="previousMonth()">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <h4 id="calendarTitle">Marec 2025</h4>
                            <button class="btn btn-secondary" onclick="nextMonth()">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                        <div id="calendarGrid" class="calendar-grid">
                            <!-- Calendar will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Task Detail Modal -->
        <div id="taskDetailModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-info-circle"></i> Detail úlohy</h3>
                    <span class="close" onclick="closeModal('taskDetailModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="taskDetailContent">
                        <!-- Task details will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Task Progress Modal -->
        <div id="taskProgressModal" class="modal">
            <div class="modal-content large">
                <div class="modal-header">
                    <h3><i class="fas fa-tasks"></i> Priebeh úlohy</h3>
                    <span class="close" onclick="closeModal('taskProgressModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="taskProgressContent">
                        <!-- Task progress will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Task Completion Modal -->
        <div id="taskCompletionModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-check-circle"></i> Úloha dokončená</h3>
                    <span class="close" onclick="closeModal('taskCompletionModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="taskCompletionContent">
                        <!-- Completion summary will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Photo Viewer Modal -->
        <div id="photoViewerModal" class="modal">
            <div class="modal-content large">
                <div class="modal-header">
                    <h3><i class="fas fa-images"></i> Fotografie</h3>
                    <span class="close" onclick="closeModal('photoViewerModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="photoViewerContent">
                        <!-- Photos will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Orders List Modal -->
        <div id="ordersListModal" class="modal">
            <div class="modal-content large">
                <div class="modal-header">
                    <h3><i class="fas fa-list"></i> Správa objednávok</h3>
                    <span class="close" onclick="closeModal('ordersListModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="orders-list-container">
                        <div class="orders-list-header">
                            <div class="orders-search">
                                <input type="text" id="ordersSearch" placeholder="Hľadať objednávky...">
                                <button class="btn btn-primary" onclick="showAddOrderModal(); closeModal('ordersListModal')">
                                    <i class="fas fa-plus"></i> Pridať novú
                                </button>
                            </div>
                        </div>
                        <div id="ordersListContent" class="orders-list-content">
                            <!-- Orders will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit Order Modal -->
        <div id="editOrderModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-edit"></i> Upraviť objednávku</h3>
                    <span class="close" onclick="closeModal('editOrderModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="editOrderForm">
                        <input type="hidden" id="editOrderId">
                        <div class="form-grid">
                            <div class="form-group">
                                <label>Meno zákazníka:</label>
                                <input type="text" id="editOrderCustomerName" required>
                            </div>
                            <div class="form-group">
                                <label>Telefón:</label>
                                <input type="tel" id="editOrderCustomerPhone" required>
                            </div>
                            <div class="form-group">
                                <label>Email:</label>
                                <input type="email" id="editOrderCustomerEmail" required>
                            </div>
                            <div class="form-group">
                                <label>Lokalita:</label>
                                <select id="editOrderLocation" required>
                                    <option value="">Vyberte lokalitu</option>
                                    <option value="bratislava">Cintorín Bratislava</option>
                                    <option value="petrzalka">Cintorín Petržalka</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Balík služieb:</label>
                                <select id="editOrderPackage" required>
                                    <option value="">Vyberte balík</option>
                                    <option value="zakladny_urnove">Základný - Urnové miesto</option>
                                    <option value="zakladny_jednohrob">Základný - Jednohrob</option>
                                    <option value="zakladny_dvojhrob">Základný - Dvojhrob</option>
                                    <option value="celorocny_urnove">Celoročný Premium - Urnové miesto</option>
                                    <option value="celorocny_jednohrob">Celoročný Premium - Jednohrob</option>
                                    <option value="celorocny_dvojhrob">Celoročný Premium - Dvojhrob</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Dátum začiatku:</label>
                                <input type="date" id="editOrderStartDate" required>
                            </div>
                            <div class="form-group">
                                <label>Zostávajúce čistenia:</label>
                                <input type="number" id="editOrderRemainingCleanings" min="0" max="100" required>
                                <small class="form-help">Aktuálny počet zostávajúcich čistení pre túto objednávku.</small>
                            </div>
                        </div>
                        <div class="modal-actions">
                            <button type="button" class="btn btn-secondary" onclick="closeModal('editOrderModal')">Zrušiť</button>
                            <button type="submit" class="btn btn-primary">Uložiť zmeny</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- CRM MODALS -->

        <!-- Add Contact Modal -->
        <div id="addContactModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-user-plus"></i> Pridať nový kontakt</h3>
                    <span class="close" onclick="closeModal('addContactModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="addContactForm" onsubmit="handleAddContact(event)">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="contactName">Meno a priezvisko *</label>
                                <input type="text" id="contactName" name="name" required>
                            </div>
                            <div class="form-group">
                                <label for="contactEmail">Email *</label>
                                <input type="email" id="contactEmail" name="email" required>
                            </div>
                            <div class="form-group">
                                <label for="contactPhone">Telefón</label>
                                <input type="tel" id="contactPhone" name="phone">
                            </div>
                            <div class="form-group">
                                <label for="contactCompany">Spoločnosť</label>
                                <input type="text" id="contactCompany" name="company">
                            </div>
                            <div class="form-group">
                                <label for="contactStatus">Status</label>
                                <select id="contactStatus" name="status">
                                    <option value="lead">Lead</option>
                                    <option value="prospect">Prospect</option>
                                    <option value="customer">Zákazník</option>
                                    <option value="inactive">Neaktívny</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="contactDealValue">Hodnota obchodu (€)</label>
                                <input type="number" id="contactDealValue" name="dealValue" min="0" step="0.01">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="contactNotes">Poznámky</label>
                            <textarea id="contactNotes" name="notes" rows="3"></textarea>
                        </div>
                        <div class="modal-actions">
                            <button type="button" class="btn btn-secondary" onclick="closeModal('addContactModal')">
                                Zrušiť
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Uložiť kontakt
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Add Task Modal -->
        <div id="addTaskModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-plus"></i> Pridať novú úlohu</h3>
                    <span class="close" onclick="closeModal('addTaskModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="addTaskForm" onsubmit="handleAddTask(event)">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="taskTitle">Názov úlohy *</label>
                                <input type="text" id="taskTitle" name="title" required>
                            </div>
                            <div class="form-group">
                                <label for="taskPriority">Priorita</label>
                                <select id="taskPriority" name="priority">
                                    <option value="low">Nízka</option>
                                    <option value="medium" selected>Stredná</option>
                                    <option value="high">Vysoká</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="taskStatus">Status</label>
                                <select id="taskStatus" name="status">
                                    <option value="new" selected>Nové</option>
                                    <option value="in_progress">V riešení</option>
                                    <option value="review">Na kontrole</option>
                                    <option value="done">Dokončené</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="taskAssignee">Pridelený užívateľ</label>
                                <input type="text" id="taskAssignee" name="assignee">
                            </div>
                            <div class="form-group">
                                <label for="taskDeadline">Termín</label>
                                <input type="date" id="taskDeadline" name="deadline">
                            </div>
                            <div class="form-group">
                                <label for="taskContact">Súvisiaci kontakt</label>
                                <select id="taskContact" name="contactId">
                                    <option value="">Vyberte kontakt...</option>
                                    <!-- Populated by JavaScript -->
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="taskDescription">Popis úlohy</label>
                            <textarea id="taskDescription" name="description" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="taskLabels">Značky (oddelené čiarkou)</label>
                            <input type="text" id="taskLabels" name="labels" placeholder="urgent, cleaning, follow-up">
                        </div>
                        <div class="modal-actions">
                            <button type="button" class="btn btn-secondary" onclick="closeModal('addTaskModal')">
                                Zrušiť
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Vytvoriť úlohu
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Add Deal Modal -->
        <div id="addDealModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-chart-line"></i> Pridať novú príležitosť</h3>
                    <span class="close" onclick="closeModal('addDealModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="addDealForm" onsubmit="handleAddDeal(event)">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="dealTitle">Názov príležitosti *</label>
                                <input type="text" id="dealTitle" name="title" required>
                            </div>
                            <div class="form-group">
                                <label for="dealValue">Hodnota (€) *</label>
                                <input type="number" id="dealValue" name="value" required min="0" step="0.01">
                            </div>
                            <div class="form-group">
                                <label for="dealStage">Štádium</label>
                                <select id="dealStage" name="stage">
                                    <option value="lead" selected>Lead</option>
                                    <option value="qualified">Kvalifikovaný</option>
                                    <option value="presentation">Prezentácia</option>
                                    <option value="proposal">Návrh</option>
                                    <option value="negotiation">Rokovanie</option>
                                    <option value="closed">Zatvorené</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="dealProbability">Pravdepodobnosť (%)</label>
                                <input type="number" id="dealProbability" name="probability" min="0" max="100" value="50">
                            </div>
                            <div class="form-group">
                                <label for="dealCompany">Spoločnosť</label>
                                <input type="text" id="dealCompany" name="company">
                            </div>
                            <div class="form-group">
                                <label for="dealCloseDate">Očakávaný termín zatvorenia</label>
                                <input type="date" id="dealCloseDate" name="expectedCloseDate">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="dealDescription">Popis</label>
                            <textarea id="dealDescription" name="description" rows="3"></textarea>
                        </div>
                        <div class="modal-actions">
                            <button type="button" class="btn btn-secondary" onclick="closeModal('addDealModal')">
                                Zrušiť
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Vytvoriť príležitosť
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Mobile Bottom Navigation (PWA) -->
        <nav class="pwa-bottom-nav" style="display: none;">
            <a href="#" class="nav-item active" data-tab="dashboard">
                <i class="fas fa-home"></i>
                <span>Dashboard</span>
            </a>
            <a href="#" class="nav-item" data-tab="quotes">
                <i class="fas fa-file-invoice"></i>
                <span>Ponuky</span>
            </a>
            <a href="#" class="nav-item" data-tab="orders">
                <i class="fas fa-clipboard-list"></i>
                <span>Objednávky</span>
            </a>
            <a href="#" class="nav-item" data-tab="clients">
                <i class="fas fa-users"></i>
                <span>Klienti</span>
            </a>
            <a href="#" class="nav-item" data-tab="email">
                <i class="fas fa-envelope"></i>
                <span>Email</span>
            </a>
        </nav>

        <!-- Mobile Menu Overlay -->
        <div class="mobile-menu-overlay" onclick="closeMobileMenu()"></div>
        <div class="mobile-menu">
            <div class="mobile-menu-header">
                <h3>Menu</h3>
                <button class="close-menu" onclick="closeMobileMenu()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="mobile-menu-items">
                <a href="#" class="mobile-menu-item" data-tab="dashboard">
                    <i class="fas fa-home"></i> Dashboard
                </a>
                <a href="#" class="mobile-menu-item" data-tab="quotes">
                    <i class="fas fa-file-invoice"></i> Cenová ponuka
                </a>
                <a href="#" class="mobile-menu-item" data-tab="invoices">
                    <i class="fas fa-file-invoice-dollar"></i> Faktúry
                </a>
                <a href="#" class="mobile-menu-item" data-tab="orders">
                    <i class="fas fa-tasks"></i> Objednávky
                </a>
                <a href="#" class="mobile-menu-item" data-tab="clients">
                    <i class="fas fa-users"></i> Klienti
                </a>
                <a href="#" class="mobile-menu-item" data-tab="services">
                    <i class="fas fa-cogs"></i> Služby
                </a>
                <a href="#" class="mobile-menu-item" data-tab="settings">
                    <i class="fas fa-cog"></i> Nastavenia
                </a>
                <a href="#" class="mobile-menu-item" data-tab="email">
                    <i class="fas fa-envelope"></i> Email
                </a>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script src="orders.js"></script>
    <script src="crm-invoicing.js"></script>
    <script src="crm-system.js"></script>
    <script src="cleaning-system.js"></script>

    <!-- Firebase Integration -->
    <script type="module" src="firebase-config.js"></script>

    <!-- Camera Service -->
    <script type="module" src="camera-service.js"></script>

    <!-- PWA Service Worker Registration -->
    <script>
        // Register Service Worker for PWA functionality
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('Service Worker registered successfully:', registration.scope);

                        // Check for updates
                        registration.addEventListener('updatefound', function() {
                            const newWorker = registration.installing;
                            newWorker.addEventListener('statechange', function() {
                                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                    // New version available
                                    if (confirm('Nová verzia aplikácie je dostupná. Chcete ju načítať?')) {
                                        window.location.reload();
                                    }
                                }
                            });
                        });
                    })
                    .catch(function(error) {
                        console.log('Service Worker registration failed:', error);
                    });
            });
        }

        // PWA Install Prompt
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', function(e) {
            console.log('PWA install prompt triggered');
            e.preventDefault();
            deferredPrompt = e;

            // Show custom install button
            showInstallButton();
        });

        function showInstallButton() {
            // Create install button if it doesn't exist
            if (!document.getElementById('pwa-install-btn')) {
                const installBtn = document.createElement('button');
                installBtn.id = 'pwa-install-btn';
                installBtn.innerHTML = '<i class="fas fa-download"></i> Inštalovať aplikáciu';
                installBtn.className = 'btn btn-primary pwa-install-button';
                installBtn.style.cssText = `
                    position: fixed;
                    bottom: 20px;
                    right: 20px;
                    z-index: 10000;
                    padding: 12px 20px;
                    background: linear-gradient(135deg, #4a1e4a 0%, #5e2e60 100%);
                    color: white;
                    border: none;
                    border-radius: 25px;
                    box-shadow: 0 4px 12px rgba(74, 30, 74, 0.3);
                    cursor: pointer;
                    font-weight: 600;
                    transition: all 0.3s ease;
                `;

                installBtn.addEventListener('click', installPWA);
                document.body.appendChild(installBtn);

                // Auto-hide after 10 seconds
                setTimeout(() => {
                    if (installBtn.parentNode) {
                        installBtn.style.opacity = '0';
                        setTimeout(() => installBtn.remove(), 300);
                    }
                }, 10000);
            }
        }

        function installPWA() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then(function(choiceResult) {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('User accepted the PWA install prompt');
                    } else {
                        console.log('User dismissed the PWA install prompt');
                    }
                    deferredPrompt = null;

                    // Remove install button
                    const installBtn = document.getElementById('pwa-install-btn');
                    if (installBtn) {
                        installBtn.remove();
                    }
                });
            }
        }

        // Handle PWA install success
        window.addEventListener('appinstalled', function(e) {
            console.log('PWA was installed successfully');

            // Remove install button if still visible
            const installBtn = document.getElementById('pwa-install-btn');
            if (installBtn) {
                installBtn.remove();
            }

            // Show success message
            if (window.showNotification) {
                showNotification('Aplikácia bola úspešne nainštalovaná!', 'success');
            }
        });

        // Check if running as PWA
        function isPWA() {
            return window.matchMedia('(display-mode: standalone)').matches ||
                   window.navigator.standalone === true;
        }

        // PWA-specific initialization
        if (isPWA()) {
            console.log('Running as PWA');
            document.body.classList.add('pwa-mode');
            showPWASplash();
        }

        // Show PWA splash screen
        function showPWASplash() {
            const splash = document.getElementById('pwa-splash');
            if (splash) {
                splash.style.display = 'flex';

                // Hide splash after 2 seconds
                setTimeout(() => {
                    splash.style.opacity = '0';
                    setTimeout(() => {
                        splash.style.display = 'none';
                    }, 500);
                }, 2000);
            }
        }

        // Network status monitoring
        function setupNetworkMonitoring() {
            const offlineIndicator = document.getElementById('offline-indicator');

            function updateNetworkStatus() {
                if (navigator.onLine) {
                    offlineIndicator.classList.remove('show');
                } else {
                    offlineIndicator.classList.add('show');
                }
            }

            window.addEventListener('online', updateNetworkStatus);
            window.addEventListener('offline', updateNetworkStatus);

            // Initial check
            updateNetworkStatus();
        }

        // Initialize network monitoring
        setupNetworkMonitoring();

        // PWA keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + K for search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.querySelector('input[type="text"]');
                if (searchInput) {
                    searchInput.focus();
                }
            }

            // Ctrl/Cmd + N for new customer
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                if (window.showAddCustomerModal) {
                    window.showAddCustomerModal();
                }
            }

            // Ctrl/Cmd + P for camera
            if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
                e.preventDefault();
                if (window.cameraService) {
                    window.cameraService.showCameraModal();
                }
            }
        });

        // Handle URL parameters for PWA shortcuts
        function handleURLParams() {
            const urlParams = new URLSearchParams(window.location.search);
            const view = urlParams.get('view');
            const action = urlParams.get('action');

            if (view && window.cleaningSystem) {
                setTimeout(() => {
                    window.cleaningSystem.switchCleaningView(view);
                }, 1000);
            }

            if (action === 'camera' && window.cameraService) {
                setTimeout(() => {
                    window.cameraService.showCameraModal();
                }, 1500);
            }
        }

        // Handle URL params after page load
        window.addEventListener('load', handleURLParams);

        // PWA update notification
        function showUpdateNotification() {
            const notification = document.createElement('div');
            notification.className = 'update-notification';
            notification.innerHTML = `
                <div class="update-content">
                    <i class="fas fa-download"></i>
                    <span>Nová verzia je dostupná</span>
                    <button onclick="window.location.reload()" class="btn btn-sm btn-primary">
                        Aktualizovať
                    </button>
                </div>
            `;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: white;
                padding: 1rem;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 10000;
                display: flex;
                align-items: center;
                gap: 1rem;
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 10000);
        }

        // Export PWA functions
        window.pwaMethods = {
            isPWA,
            showPWASplash,
            showUpdateNotification,
            handleURLParams
        };
    </script>
</body>
</html>
