// Application State
let selectedServices = [];
let customerData = {};
let currentDiscount = 0; // Current discount percentage (0, 5, 10, 15)

// Check if running in Electron
const isElectron = typeof window !== 'undefined' && window.process && window.process.type;

// Electron specific functionality
if (isElectron) {
    const { ipc<PERSON>enderer } = require('electron');

    // Enhanced save/load functionality for Electron
    window.electronAPI = {
        saveFile: async (data, filename) => {
            const result = await ipcRenderer.invoke('show-save-dialog', {
                defaultPath: filename,
                filters: [
                    { name: 'JSON súbory', extensions: ['json'] },
                    { name: '<PERSON><PERSON><PERSON><PERSON> súbory', extensions: ['*'] }
                ]
            });

            if (!result.canceled) {
                const writeResult = await ipcRenderer.invoke('write-file', result.filePath, data);
                return writeResult;
            }
            return { success: false, canceled: true };
        },

        openFile: async () => {
            const result = await ipc<PERSON>enderer.invoke('show-open-dialog', {
                properties: ['openFile'],
                filters: [
                    { name: 'JSON súbory', extensions: ['json'] },
                    { name: '<PERSON><PERSON><PERSON><PERSON> súbory', extensions: ['*'] }
                ]
            });

            if (!result.canceled && result.filePaths.length > 0) {
                const readResult = await ipcRenderer.invoke('read-file', result.filePaths[0]);
                return readResult;
            }
            return { success: false, canceled: true };
        }
    };
}

// DOM Elements (will be initialized after DOM loads)
let serviceCheckboxes;
let customerForm;
let selectedServicesList;
let subtotalElement;
let vatElement;
let totalElement;
let generatePDFButton;
let discountElements;
let discountRowElement;
let discountPercentElement;
let discountAmountElement;
let subtotalAfterDiscountElement;

// Initialize Application
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DOM elements
    serviceCheckboxes = document.querySelectorAll('input[type="checkbox"][data-service]');
    customerForm = document.querySelector('.customer-section');
    selectedServicesList = document.getElementById('selectedServicesList');
    subtotalElement = document.getElementById('subtotal');
    vatElement = document.getElementById('vat');
    totalElement = document.getElementById('total');
    generatePDFButton = document.querySelector('.btn-generate');

    // Discount elements
    discountElements = document.querySelectorAll('input[name="discount"]');
    discountRowElement = document.getElementById('discountRow');
    discountPercentElement = document.getElementById('discountPercent');
    discountAmountElement = document.getElementById('discountAmount');
    subtotalAfterDiscountElement = document.getElementById('subtotalAfterDiscount');

    initializeEventListeners();
    updateCalculation();
    initializeNavigation();

    // Check if html2pdf is loaded
    setTimeout(checkHtml2PdfAvailability, 1000);

    // Initialize tab system
    initializeTabSystem();
});

function checkHtml2PdfAvailability() {
    console.log('Checking html2pdf availability...');
    console.log('generatePDFButton:', generatePDFButton);

    if (typeof window.html2pdf === 'undefined') {
        console.warn('html2pdf is not available, PDF generation will not work');
        if (generatePDFButton) {
            generatePDFButton.title = 'html2pdf knižnica nie je dostupná';
            generatePDFButton.style.opacity = '0.5';
        }
    } else {
        console.log('html2pdf is available and ready');
        if (generatePDFButton) {
            generatePDFButton.title = 'Generovať PDF ponuku';
            generatePDFButton.style.opacity = '1';
        }
    }
}

function initializeTabSystem() {
    // Set default active tab
    const defaultTab = 'quotes';
    switchTab(defaultTab);

    // Close modals when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
            event.target.classList.remove('show');
            event.target.style.display = 'none';
        }
    });
}

// Event Listeners
function initializeEventListeners() {
    // Service selection
    if (serviceCheckboxes) {
        serviceCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', handleServiceChange);
        });
    }

    // Custom input fields
    document.addEventListener('input', handleCustomInputChange);

    // Customer form
    if (customerForm) {
        const customerInputs = customerForm.querySelectorAll('input');
        customerInputs.forEach(input => {
            input.addEventListener('input', updateCustomerData);
        });
    }

    // Generate PDF button
    if (generatePDFButton) {
        generatePDFButton.addEventListener('click', generatePDF);
    }

    // Discount selection
    if (discountElements) {
        discountElements.forEach(radio => {
            radio.addEventListener('change', handleDiscountChange);
        });
    }

    // Navigation
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', handleNavigation);
    });
}

// Navigation
function initializeNavigation() {
    // Categories are handled by CSS classes now
}

function handleNavigation(e) {
    e.preventDefault();
    const targetId = e.currentTarget.getAttribute('href').substring(1);

    // Update active nav link
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    e.currentTarget.classList.add('active');

    // Show target category
    document.querySelectorAll('.service-category').forEach(category => {
        category.classList.remove('active');
    });

    const targetCategory = document.getElementById(targetId);
    if (targetCategory) {
        targetCategory.classList.add('active', 'fade-in');
    }
}

// Service Selection
function handleServiceChange(e) {
    const checkbox = e.target;
    const serviceName = checkbox.getAttribute('data-service');
    const basePrice = parseFloat(checkbox.getAttribute('data-price')) || 0;
    const isCustom = checkbox.hasAttribute('data-custom');
    
    if (checkbox.checked) {
        let finalPrice = basePrice;
        
        // Handle custom pricing
        if (isCustom) {
            finalPrice = calculateCustomPrice(checkbox);
        }
        
        const service = {
            name: serviceName,
            price: finalPrice,
            basePrice: basePrice,
            isCustom: isCustom,
            element: checkbox
        };
        
        selectedServices.push(service);
        
        // Show custom input if needed
        showCustomInput(checkbox);
    } else {
        // Remove service from selection
        selectedServices = selectedServices.filter(service => service.name !== serviceName);
        
        // Hide custom input
        hideCustomInput(checkbox);
    }
    
    updateCalculation();
    updateSelectedServicesList();
}

function calculateCustomPrice(checkbox) {
    const serviceItem = checkbox.closest('.service-item');
    const customInput = serviceItem.querySelector('.custom-input input');
    const multiplier = parseFloat(customInput.getAttribute('data-multiplier'));
    const additional = parseFloat(customInput.getAttribute('data-additional'));
    const basePrice = parseFloat(checkbox.getAttribute('data-price')) || 0;
    
    if (customInput && customInput.value) {
        const inputValue = parseFloat(customInput.value) || 0;
        
        if (multiplier) {
            return inputValue * multiplier;
        } else if (additional) {
            return inputValue + additional;
        }
    }
    
    return basePrice;
}

function handleCustomInputChange(e) {
    const input = e.target;
    if (input.closest('.custom-input')) {
        const serviceItem = input.closest('.service-item');
        const checkbox = serviceItem.querySelector('input[type="checkbox"]');
        
        if (checkbox && checkbox.checked) {
            // Update the service price
            const serviceName = checkbox.getAttribute('data-service');
            const newPrice = calculateCustomPrice(checkbox);
            
            // Update in selectedServices array
            const serviceIndex = selectedServices.findIndex(service => service.name === serviceName);
            if (serviceIndex !== -1) {
                selectedServices[serviceIndex].price = newPrice;
                updateCalculation();
                updateSelectedServicesList();
            }
        }
    }
}

function showCustomInput(checkbox) {
    const serviceItem = checkbox.closest('.service-item');
    const customInput = serviceItem.querySelector('.custom-input');
    if (customInput) {
        customInput.style.display = 'block';
        customInput.classList.add('slide-in');
    }
}

function hideCustomInput(checkbox) {
    const serviceItem = checkbox.closest('.service-item');
    const customInput = serviceItem.querySelector('.custom-input');
    if (customInput) {
        customInput.style.display = 'none';
        customInput.classList.remove('slide-in');
        // Reset input value
        const input = customInput.querySelector('input');
        if (input) {
            input.value = '';
        }
    }
}

// Discount handling
function handleDiscountChange(e) {
    currentDiscount = parseInt(e.target.value);
    updateCalculation();
}

// Calculation
function updateCalculation() {
    const subtotal = selectedServices.reduce((sum, service) => sum + service.price, 0);

    // Calculate discount
    const discountAmount = subtotal * (currentDiscount / 100);
    const subtotalAfterDiscount = subtotal - discountAmount;

    // Calculate VAT on discounted amount
    const vat = subtotalAfterDiscount * 0.20; // 20% DPH
    const total = subtotalAfterDiscount + vat;

    // Update display
    if (subtotalElement) subtotalElement.textContent = formatPrice(subtotal);

    // Show/hide discount row
    if (discountRowElement) {
        if (currentDiscount > 0) {
            discountRowElement.style.display = 'flex';
            if (discountPercentElement) discountPercentElement.textContent = currentDiscount;
            if (discountAmountElement) discountAmountElement.textContent = '-' + formatPrice(discountAmount);
        } else {
            discountRowElement.style.display = 'none';
        }
    }

    if (subtotalAfterDiscountElement) subtotalAfterDiscountElement.textContent = formatPrice(subtotalAfterDiscount);
    if (vatElement) vatElement.textContent = formatPrice(vat);
    if (totalElement) totalElement.textContent = formatPrice(total);

    // Enable/disable PDF button
    if (generatePDFButton) {
        const hasServices = selectedServices.length > 0;
        const hasCustomerData = validateCustomerData();
        generatePDFButton.disabled = !hasServices || !hasCustomerData;
    }
}

function updateSelectedServicesList() {
    if (!selectedServicesList) return;

    if (selectedServices.length === 0) {
        selectedServicesList.innerHTML = '<p class="no-services">Žiadne služby nie sú vybrané</p>';
        return;
    }
    
    const servicesHTML = selectedServices.map(service => `
        <div class="selected-service">
            <span class="service-name-calc">${service.name}</span>
            <span class="service-price-calc">${formatPrice(service.price)}</span>
        </div>
    `).join('');
    
    selectedServicesList.innerHTML = servicesHTML;
}

function formatPrice(price) {
    return new Intl.NumberFormat('sk-SK', {
        style: 'currency',
        currency: 'EUR',
        minimumFractionDigits: 2
    }).format(price);
}

// Customer Data
function updateCustomerData() {
    customerData = {
        name: document.getElementById('customerName').value,
        phone: document.getElementById('customerPhone').value,
        email: document.getElementById('customerEmail').value,
        address: document.getElementById('customerAddress').value,
        cemetery: document.getElementById('cemetery').value
    };
    
    updateCalculation();
}

function validateCustomerData() {
    return customerData.name && 
           customerData.phone && 
           customerData.email && 
           isValidEmail(customerData.email);
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Function to get all available services from the quote form
function getAllAvailableServices() {
    const services = [];
    const serviceCheckboxes = document.querySelectorAll('input[type="checkbox"][data-service]');

    serviceCheckboxes.forEach(checkbox => {
        const serviceName = checkbox.getAttribute('data-service');
        const basePrice = parseFloat(checkbox.getAttribute('data-price')) || 0;
        const isCustom = checkbox.hasAttribute('data-custom');

        services.push({
            id: serviceName.toLowerCase().replace(/[^a-z0-9]/g, '_'),
            name: serviceName,
            price: basePrice,
            isCustom: isCustom,
            category: getServiceCategory(checkbox)
        });
    });

    return services;
}

// Function to get service category based on its location in DOM
function getServiceCategory(checkbox) {
    const serviceCategory = checkbox.closest('.service-category');
    if (serviceCategory) {
        const categoryId = serviceCategory.id;
        switch(categoryId) {
            case 'basic-services': return 'basic';
            case 'packages': return 'package';
            case 'digital': return 'digital';
            case 'additional': return 'additional';
            case 'special': return 'special';
            default: return 'other';
        }
    }
    return 'other';
}

// Function to get currently selected services (for creating orders/invoices)
function getSelectedServicesData() {
    return selectedServices.map(service => ({
        id: service.name.toLowerCase().replace(/[^a-z0-9]/g, '_'),
        name: service.name,
        price: service.price,
        quantity: 1,
        total: service.price,
        isCustom: service.isCustom || false
    }));
}

// Function to create order from current quote
function createOrderFromQuote() {
    if (selectedServices.length === 0) {
        alert('Prosím vyberte aspoň jednu službu.');
        return;
    }

    if (!validateCustomerData()) {
        alert('Prosím vyplňte všetky povinné údaje o zákazníkovi.');
        return;
    }

    // Get customer data
    const customerName = document.getElementById('customerName').value;
    const customerPhone = document.getElementById('customerPhone').value;
    const customerEmail = document.getElementById('customerEmail').value;
    const customerAddress = document.getElementById('customerAddress').value;
    const cemetery = document.getElementById('cemetery').value;

    // Prepare quote data
    const quoteData = {
        customer: {
            name: customerName,
            phone: customerPhone,
            email: customerEmail,
            address: customerAddress,
            cemetery: cemetery
        },
        services: getSelectedServicesData(),
        discount: currentDiscount,
        subtotal: selectedServices.reduce((sum, service) => sum + service.price, 0),
        total: calculateTotal()
    };

    // Switch to orders tab and create order
    switchTab('orders');

    // Call order creation function if available
    if (typeof createOrderFromQuoteData === 'function') {
        createOrderFromQuoteData(quoteData);
    } else {
        alert('Systém objednávok nie je dostupný. Objednávku vytvorte manuálne v sekcii Objednávky.');
    }
}

function calculateTotal() {
    const subtotal = selectedServices.reduce((sum, service) => sum + service.price, 0);
    const discountAmount = subtotal * (currentDiscount / 100);
    const subtotalAfterDiscount = subtotal - discountAmount;
    const vat = subtotalAfterDiscount * 0.2;
    return subtotalAfterDiscount + vat;
}

// PDF Generation with html2pdf for perfect Unicode support
async function generatePDF() {
    if (selectedServices.length === 0) {
        alert('Prosím vyberte aspoň jednu službu.');
        return;
    }

    if (!validateCustomerData()) {
        alert('Prosím vyplňte všetky povinné údaje o zákazníkovi.');
        return;
    }

    // Show loading state
    if (generatePDFButton) {
        generatePDFButton.classList.add('loading');
        generatePDFButton.disabled = true;
        generatePDFButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generujem PDF...';
    }

    try {
        // Check if html2pdf is available
        if (typeof window.html2pdf === 'undefined') {
            throw new Error('html2pdf knižnica nie je dostupná');
        }

        // Load the PDF template
        const templateResponse = await fetch('pdf-template.html');
        if (!templateResponse.ok) {
            throw new Error('Nepodarilo sa načítať PDF template');
        }

        const templateHTML = await templateResponse.text();

        // Create a temporary container
        const tempContainer = document.createElement('div');
        tempContainer.innerHTML = templateHTML;
        tempContainer.style.position = 'absolute';
        tempContainer.style.left = '-9999px';
        tempContainer.style.top = '-9999px';
        document.body.appendChild(tempContainer);

        // Fill in customer data
        const customerNameEl = tempContainer.querySelector('#customer-name');
        const customerPhoneEl = tempContainer.querySelector('#customer-phone');
        const customerEmailEl = tempContainer.querySelector('#customer-email');
        const customerAddressEl = tempContainer.querySelector('#customer-address');

        if (customerNameEl) customerNameEl.textContent = customerData.name || '-';
        if (customerPhoneEl) customerPhoneEl.textContent = customerData.phone || '-';
        if (customerEmailEl) customerEmailEl.textContent = customerData.email || '-';

        // Combine address and cemetery
        let fullAddress = '';
        if (customerData.address) fullAddress += customerData.address;
        if (customerData.cemetery) {
            if (fullAddress) fullAddress += ', ';
            fullAddress += `Cintorín: ${customerData.cemetery}`;
        }
        if (customerAddressEl) customerAddressEl.textContent = fullAddress || '-';
        // Fill in services
        const servicesListEl = tempContainer.querySelector('#services-list');
        if (servicesListEl) {
            servicesListEl.innerHTML = '';
            selectedServices.forEach((service, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="service-name">${service.name}</td>
                    <td style="text-align: center;">1</td>
                    <td class="service-price">${formatPrice(service.price)}</td>
                `;
                servicesListEl.appendChild(row);
            });
        }
        // Calculate and fill in totals
        const subtotal = selectedServices.reduce((sum, service) => sum + service.price, 0);

        // Calculate discount
        const discountAmount = subtotal * (currentDiscount / 100);
        const subtotalAfterDiscount = subtotal - discountAmount;

        // Calculate VAT on discounted amount
        const vat = subtotalAfterDiscount * 0.20;
        const total = subtotalAfterDiscount + vat;

        const subtotalEl = tempContainer.querySelector('#subtotal');
        const vatEl = tempContainer.querySelector('#vat');
        const totalEl = tempContainer.querySelector('#total');
        const issueDateEl = tempContainer.querySelector('#issue-date');

        // Discount elements
        const discountRowPdfEl = tempContainer.querySelector('#discountRowPdf');
        const discountPercentPdfEl = tempContainer.querySelector('#discountPercentPdf');
        const discountAmountPdfEl = tempContainer.querySelector('#discountAmountPdf');
        const subtotalAfterDiscountPdfEl = tempContainer.querySelector('#subtotalAfterDiscountPdf');

        if (subtotalEl) subtotalEl.textContent = formatPrice(subtotal);

        // Show/hide discount row in PDF
        if (discountRowPdfEl) {
            if (currentDiscount > 0) {
                discountRowPdfEl.style.display = 'table-row';
                if (discountPercentPdfEl) discountPercentPdfEl.textContent = currentDiscount;
                if (discountAmountPdfEl) discountAmountPdfEl.textContent = '-' + formatPrice(discountAmount);
            } else {
                discountRowPdfEl.style.display = 'none';
            }
        }

        if (subtotalAfterDiscountPdfEl) subtotalAfterDiscountPdfEl.textContent = formatPrice(subtotalAfterDiscount);
        if (vatEl) vatEl.textContent = formatPrice(vat);
        if (totalEl) totalEl.textContent = formatPrice(total);
        if (issueDateEl) issueDateEl.textContent = new Date().toLocaleDateString('sk-SK');

        // Get the PDF content element
        const pdfContent = tempContainer.querySelector('#pdf-content');
        if (!pdfContent) {
            throw new Error('PDF template element not found');
        }

        // Configure html2pdf options
        const options = {
            margin: [10, 10, 10, 10],
            filename: `cenova-ponuka-${customerData.name.replace(/\s+/g, '-').toLowerCase()}-${new Date().toISOString().split('T')[0]}.pdf`,
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: {
                scale: 2,
                useCORS: true,
                allowTaint: true,
                letterRendering: true
            },
            jsPDF: {
                unit: 'mm',
                format: 'a4',
                orientation: 'portrait',
                compress: true
            }
        };

        // Generate and save PDF
        await html2pdf().set(options).from(pdfContent).save();

        // Clean up
        document.body.removeChild(tempContainer);

    } catch (error) {
        console.error('Error generating PDF:', error);
        let errorMessage = 'Nastala chyba pri generovaní PDF.';

        if (error.message.includes('html2pdf')) {
            errorMessage = 'html2pdf knižnica nie je dostupná. Skúste obnoviť aplikáciu.';
        } else if (error.message.includes('template')) {
            errorMessage = 'Nepodarilo sa načítať PDF template.';
        } else if (error.message.includes('save')) {
            errorMessage = 'Chyba pri ukladaní PDF súboru.';
        }

        alert(errorMessage + '\n\nDetail chyby: ' + error.message);

        // Clean up temp container if it exists
        const tempContainer = document.querySelector('div[style*="-9999px"]');
        if (tempContainer) {
            document.body.removeChild(tempContainer);
        }
    } finally {
        // Reset button state
        if (generatePDFButton) {
            generatePDFButton.classList.remove('loading');
            generatePDFButton.disabled = false;
            generatePDFButton.innerHTML = '<i class="fas fa-file-pdf"></i> Generovať PDF ponuku';
        }
        updateCalculation(); // This will re-enable the button if conditions are met
    }
}

// Export functions for global access (for CRM integration)
window.getAllAvailableServices = getAllAvailableServices;
window.getSelectedServicesData = getSelectedServicesData;
window.createOrderFromQuote = createOrderFromQuote;

// Mobile Navigation Enhancement
function initializeMobileNavigation() {
    const isMobile = window.innerWidth <= 768;
    const bottomNav = document.querySelector('.pwa-bottom-nav');
    const mainNav = document.querySelector('.main-nav');

    if (isMobile && bottomNav) {
        // Show bottom navigation on mobile
        bottomNav.style.display = 'flex';

        // Hide main navigation on mobile
        if (mainNav) {
            mainNav.style.display = 'none';
        }

        // Add click handlers for bottom navigation
        const bottomNavItems = bottomNav.querySelectorAll('.nav-item');
        bottomNavItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const tabName = item.getAttribute('data-tab');

                // Update active state in bottom nav
                bottomNavItems.forEach(navItem => navItem.classList.remove('active'));
                item.classList.add('active');

                // Trigger tab change
                if (tabName && typeof switchTab === 'function') {
                    switchTab(tabName);
                } else if (tabName) {
                    // Fallback tab switching if switchTab is not available
                    const mainNavTabs = document.querySelectorAll('.nav-tab');
                    mainNavTabs.forEach(tab => {
                        tab.classList.remove('active');
                        if (tab.getAttribute('data-tab') === tabName) {
                            tab.classList.add('active');
                        }
                    });

                    // Update tab panes
                    document.querySelectorAll('.tab-pane').forEach(pane => {
                        pane.classList.remove('active');
                    });
                    const activePane = document.getElementById(`${tabName}-tab`);
                    if (activePane) {
                        activePane.classList.add('active');
                    }
                }
            });
        });
    } else if (bottomNav) {
        // Hide bottom navigation on desktop
        bottomNav.style.display = 'none';

        // Show main navigation on desktop
        if (mainNav) {
            mainNav.style.display = 'flex';
        }
    }
}

// Enhanced mobile detection and responsive behavior
function handleResponsiveChanges() {
    const isMobile = window.innerWidth <= 768;
    const body = document.body;

    if (isMobile) {
        body.classList.add('mobile-view');
        body.classList.remove('desktop-view');

        // Add PWA mode class for mobile-specific styles
        if (window.matchMedia('(display-mode: standalone)').matches ||
            window.navigator.standalone === true) {
            body.classList.add('pwa-mode');
        }
    } else {
        body.classList.add('desktop-view');
        body.classList.remove('mobile-view', 'pwa-mode');
    }

    // Reinitialize mobile navigation
    initializeMobileNavigation();
}

// Touch-friendly enhancements for mobile
function initializeTouchEnhancements() {
    if ('ontouchstart' in window) {
        document.body.classList.add('touch-device');

        // Add touch feedback to buttons
        const buttons = document.querySelectorAll('button, .btn, .nav-tab, .nav-item');
        buttons.forEach(button => {
            button.addEventListener('touchstart', function() {
                this.classList.add('touch-active');
            });

            button.addEventListener('touchend', function() {
                setTimeout(() => {
                    this.classList.remove('touch-active');
                }, 150);
            });
        });
    }
}

// Initialize mobile features when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize responsive behavior
    handleResponsiveChanges();

    // Initialize touch enhancements
    initializeTouchEnhancements();

    // Listen for window resize
    window.addEventListener('resize', handleResponsiveChanges);

    // Listen for orientation change on mobile
    window.addEventListener('orientationchange', function() {
        setTimeout(handleResponsiveChanges, 100);
    });
});

// PWA specific mobile enhancements
if ('serviceWorker' in navigator) {
    // Add PWA install prompt handling
    let deferredPrompt;

    window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;

        // Show install button on mobile
        if (window.innerWidth <= 768) {
            showPWAInstallPrompt();
        }
    });

    function showPWAInstallPrompt() {
        const installButton = document.createElement('button');
        installButton.className = 'pwa-install-button';
        installButton.innerHTML = '<i class="fas fa-download"></i> Nainštalovať aplikáciu';
        installButton.style.cssText = `
            position: fixed;
            bottom: 80px;
            right: 20px;
            background: #5e2e60;
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 25px;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 1001;
            display: flex;
            align-items: center;
            gap: 8px;
        `;

        installButton.addEventListener('click', async () => {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;

                if (outcome === 'accepted') {
                    console.log('PWA installed');
                }

                deferredPrompt = null;
                installButton.remove();
            }
        });

        document.body.appendChild(installButton);

        // Auto-hide after 10 seconds
        setTimeout(() => {
            if (installButton.parentNode) {
                installButton.remove();
            }
        }, 10000);
    }
}




