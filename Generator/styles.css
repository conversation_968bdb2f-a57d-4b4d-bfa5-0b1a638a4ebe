/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--neutral-dark);
    background: linear-gradient(135deg, var(--neutral-light) 0%, #e9ecef 100%);
    -webkit-app-region: no-drag; /* Ensure body doesn't interfere with dragging */
    font-weight: 400;
    font-size: 16px;
}

/* New Color Variables */
:root {
    --primary-color: #5e2e60;
    --secondary-color: #10b981;
    --accent-color: #f59e0b;
    --neutral-dark: #111827;
    --neutral-medium: #6b7280;
    --neutral-light: #f9fafb;
    --purple-primary: #5e2e60;
    --purple-secondary: #4a1e4a;
    --teal-accent: #327881;
}

.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* TYPOGRAPHY SYSTEM */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: var(--neutral-dark);
    line-height: 1.3;
    margin: 0;
}

h1 {
    font-size: 2.5rem;
    font-weight: 700;
    letter-spacing: -0.025em;
}

h2 {
    font-size: 2rem;
    font-weight: 600;
    letter-spacing: -0.025em;
}

h3 {
    font-size: 1.5rem;
    font-weight: 600;
    letter-spacing: -0.025em;
}

h4 {
    font-size: 1.25rem;
    font-weight: 600;
}

h5 {
    font-size: 1.125rem;
    font-weight: 500;
}

h6 {
    font-size: 1rem;
    font-weight: 500;
}

p {
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.6;
    color: var(--neutral-medium);
    margin: 0;
}

.text-large {
    font-size: 1.125rem;
    line-height: 1.6;
}

.text-small {
    font-size: 0.875rem;
    line-height: 1.5;
}

.text-xs {
    font-size: 0.75rem;
    line-height: 1.4;
}

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--secondary-color); }
.text-accent { color: var(--accent-color); }
.text-neutral { color: var(--neutral-medium); }
.text-dark { color: var(--neutral-dark); }

/* Header Styles */
.header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--purple-secondary) 100%);
    color: white;
    padding: 1.5rem 0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
}

/* Main Navigation */
.main-nav {
    display: flex;
    gap: 0.5rem;
}

.desktop-nav {
    display: flex;
}

/* Mobile Navigation Header */
.mobile-nav-header {
    display: none;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    margin-top: 1rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.back-button, .menu-button {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: background 0.2s ease;
    position: relative;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Hamburger Menu Animation */
.hamburger {
    width: 20px;
    height: 2px;
    background: white;
    position: relative;
    transition: all 0.3s ease;
}

.hamburger::before,
.hamburger::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 2px;
    background: white;
    transition: all 0.3s ease;
}

.hamburger::before {
    top: -6px;
}

.hamburger::after {
    top: 6px;
}

.menu-button.active .hamburger {
    background: transparent;
}

.menu-button.active .hamburger::before {
    transform: rotate(45deg);
    top: 0;
}

.menu-button.active .hamburger::after {
    transform: rotate(-45deg);
    top: 0;
}

.back-button:hover, .menu-button:hover {
    background: rgba(255, 255, 255, 0.1);
}

.current-section-title {
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
}

/* Mobile Menu */
.mobile-menu-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.mobile-menu {
    display: none;
    position: fixed;
    top: 0;
    right: -320px;
    width: 320px;
    max-width: 85vw;
    height: 100vh;
    background: white;
    z-index: 1001;
    transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);
    overflow-y: auto;
}

.mobile-menu.open {
    right: 0;
}

.mobile-menu.show {
    display: block;
}

.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
    background: var(--primary-color);
    color: white;
}

.mobile-menu-header h3 {
    margin: 0;
    font-size: 1.2rem;
}

.close-menu {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
}

.mobile-menu-items {
    padding: 1rem 0;
}

.mobile-menu-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    color: var(--neutral-dark);
    text-decoration: none;
    transition: background 0.2s ease;
    border-left: 3px solid transparent;
}

.mobile-menu-item:hover {
    background: #f3f4f6;
    border-left-color: var(--primary-color);
}

.mobile-menu-item.active {
    background: rgba(37, 99, 235, 0.1);
    border-left-color: var(--primary-color);
    color: var(--primary-color);
}

.mobile-menu-item i {
    width: 20px;
    text-align: center;
}

.nav-tab {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-tab:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.nav-tab.active {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-image {
    height: 60px;
    width: auto;
    max-width: 80px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    object-fit: contain; /* Prevents logo squishing */
    filter: brightness(0) invert(1); /* Makes logo white */
}

.logo-text {
    display: flex;
    flex-direction: column;
}

.logo-icon {
    font-size: 2.5rem;
    color: #327881;
}

.logo-section h1 {
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0;
    color: white; /* White text on purple background */
}

.subtitle {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-left: 0.5rem;
    color: white; /* White subtitle on purple background */
}

.header-actions {
    display: flex;
    gap: 1rem;
}

/* Button Styles */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--purple-secondary) 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(94, 46, 96, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(94, 46, 96, 0.4);
    background: linear-gradient(135deg, var(--purple-secondary) 0%, var(--primary-color) 100%);
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
    border: 1px solid var(--secondary-color);
}

.btn-secondary:hover {
    background: #059669;
    border-color: #059669;
    transform: translateY(-1px);
}

.btn-accent {
    background: var(--accent-color);
    color: white;
    border: 1px solid var(--accent-color);
}

.btn-accent:hover {
    background: #d97706;
    border-color: #d97706;
    transform: translateY(-1px);
}

.btn-outline-primary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.btn-generate {
    width: 100%;
    padding: 1rem;
    font-size: 1rem;
    margin-top: 1rem;
}

/* Main Content Layout */
.main-content {
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

/* Tab System */
.tab-content {
    width: 100%;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.quotes-layout {
    display: grid;
    grid-template-columns: 250px 1fr 300px;
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

/* Updated Quotes Section Styles */
.customer-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    margin-bottom: 2rem;
}

.customer-section h2 {
    color: var(--neutral-dark);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.services-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.service-category h3 {
    color: var(--neutral-dark);
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e5e7eb;
}

.service-subcategory {
    margin-bottom: 2rem;
}

.service-subcategory h4 {
    color: var(--neutral-dark);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.service-item {
    margin-bottom: 1rem;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
}

.service-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.1);
}

.service-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    cursor: pointer;
    background: white;
    transition: background 0.2s ease;
}

.service-label:hover {
    background: #f8fafc;
}

.service-name {
    font-weight: 500;
    color: var(--neutral-dark);
    flex: 1;
    margin-left: 0.75rem;
}

.service-price {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 1rem;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #e5e7eb;
    border-radius: 4px;
    position: relative;
    transition: all 0.2s ease;
}

input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.custom-input {
    padding: 1rem;
    background: #f8fafc;
    border-top: 1px solid #e5e7eb;
    display: none;
}

.service-item input[type="checkbox"]:checked ~ .custom-input {
    display: block;
}

.custom-input label {
    font-weight: 500;
    color: var(--neutral-dark);
    margin-bottom: 0.5rem;
    display: block;
}

.custom-input input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 0.9rem;
}

.orders-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.settings-layout {
    max-width: 800px;
}

/* Sidebar Navigation */
.sidebar {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    height: fit-content;
    position: sticky;
    top: 2rem;
}

.sidebar h3 {
    color: var(--neutral-dark);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.service-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.service-nav li {
    margin-bottom: 0.5rem;
}

.service-nav .nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    color: var(--neutral-medium);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.2s ease;
    font-weight: 500;
}

.service-nav .nav-link:hover {
    background: #f8fafc;
    color: var(--primary-color);
}

.service-nav .nav-link.active {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
    border-left: 3px solid var(--primary-color);
}

/* Calculator Sidebar */
.calculator {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    height: fit-content;
    position: sticky;
    top: 2rem;
}

.calculator-content h3 {
    color: var(--neutral-dark);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.selected-services h4 {
    color: var(--neutral-dark);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.services-list {
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 1rem;
    padding: 0.5rem;
    background: #f8fafc;
    border-radius: 8px;
}

.no-services {
    color: var(--neutral-medium);
    font-style: italic;
    text-align: center;
    padding: 1rem;
}

.discount-section h4 {
    color: var(--neutral-dark);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.discount-options {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.discount-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.discount-option:hover {
    background: #f8fafc;
}

.discount-option input[type="radio"] {
    margin: 0;
}

.price-summary {
    border-top: 1px solid #e5e7eb;
    padding-top: 1rem;
}

.service-nav h3 {
    color: #5e2e60;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.service-nav ul {
    list-style: none;
}

.service-nav li {
    margin-bottom: 0.5rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #666;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.nav-link:hover,
.nav-link.active {
    background: linear-gradient(135deg, #5e2e60 0%, #4a1e4a 100%);
    color: white;
    transform: translateX(4px);
}

.nav-link i {
    width: 16px;
    text-align: center;
}

/* Form Area */
.form-area {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* Customer Section */
.customer-section {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 2px solid #f0f0f0;
}

.customer-section h2 {
    color: #5e2e60;
    margin-bottom: 1.5rem;
    font-size: 1.4rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.form-group input {
    padding: 0.75rem;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #5e2e60;
    box-shadow: 0 0 0 3px rgba(94, 46, 96, 0.1);
}

.form-group input:required:invalid {
    border-color: #e74c3c;
}

.form-help {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #666;
    font-style: italic;
}

/* Services Section */
.services-section {
    margin-top: 2rem;
}

.service-category {
    margin-bottom: 3rem;
    display: none;
}

.service-category.active {
    display: block;
}

.service-category h3 {
    color: #5e2e60;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #9f7ba0;
}

.services-grid {
    display: grid;
    gap: 2rem;
}

.service-subcategory {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 4px solid #327881;
}

.service-subcategory h4 {
    color: #5e2e60;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.service-item {
    margin-bottom: 1rem;
    position: relative;
}

.service-label {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.service-label:hover {
    border-color: #327881;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.service-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #d0d0d0;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.service-label input[type="checkbox"]:checked + .checkmark {
    background: #5e2e60;
    border-color: #5e2e60;
}

.service-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.service-name {
    flex: 1;
    font-weight: 500;
    color: #333;
}

.service-price {
    font-weight: 600;
    color: #327881;
    font-size: 1rem;
}

.service-label input[type="checkbox"]:checked ~ .service-name {
    color: #5e2e60;
}

.service-label input[type="checkbox"]:checked {
    border-color: #5e2e60;
    background: rgba(94, 46, 96, 0.05);
}

/* Custom Input Fields */
.custom-input {
    margin-top: 0.5rem;
    padding: 0.75rem;
    background: #f0f0f0;
    border-radius: 6px;
    display: none;
}

.service-item:has(input[type="checkbox"]:checked) .custom-input {
    display: block;
}

.custom-input label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
    font-weight: 500;
    color: #666;
}

.custom-input input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 0.9rem;
}

/* Calculator Sidebar */
.calculator {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    height: fit-content;
    position: sticky;
    top: 2rem;
}

.calculator-content h3 {
    color: #5e2e60;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.selected-services {
    margin-bottom: 1.5rem;
}

.selected-services h4 {
    color: #666;
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

.services-list {
    max-height: 200px;
    overflow-y: auto;
}

.no-services {
    color: #999;
    font-style: italic;
    text-align: center;
    padding: 1rem;
}

.selected-service {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
    font-size: 0.85rem;
}

.selected-service:last-child {
    border-bottom: none;
}

.service-name-calc {
    flex: 1;
    color: #333;
}

.service-price-calc {
    color: #327881;
    font-weight: 600;
}

/* Discount Section */
.discount-section {
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.discount-section h4 {
    margin: 0 0 0.75rem 0;
    color: #5e2e60;
    font-size: 0.95rem;
    font-weight: 600;
}

.discount-options {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.discount-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.9rem;
    color: #333;
    transition: color 0.2s ease;
}

.discount-option:hover {
    color: #5e2e60;
}

.discount-option input[type="radio"] {
    margin-right: 0.5rem;
    accent-color: #5e2e60;
}

.discount-option span {
    user-select: none;
}

.price-summary {
    border-top: 2px solid #f0f0f0;
    padding-top: 1rem;
}

.price-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.price-row.discount-row {
    color: #dc3545;
    font-weight: 500;
}

.price-row.total {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    border-top: 1px solid #e0e0e0;
    padding-top: 0.5rem;
    margin-top: 0.5rem;
}

/* RESPONSIVE GRID SYSTEM */

/* Extra Large Screens (1400px+) */
@media (min-width: 1400px) {
    .dashboard-container {
        max-width: 1400px;
        padding: 3rem;
    }

    .dashboard-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 2.5rem;
    }

    .dashboard-button {
        min-height: 220px;
        padding: 2.5rem;
    }

    .button-icon {
        font-size: 3.5rem;
    }

    .button-content h3 {
        font-size: 1.4rem;
    }
}

/* Large Screens (1200px - 1399px) */
@media (max-width: 1399px) and (min-width: 1200px) {
    .dashboard-container {
        max-width: 1200px;
        padding: 2.5rem;
    }

    .dashboard-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
    }

    .dashboard-button {
        min-height: 200px;
        padding: 2rem;
    }
}

/* Medium-Large Screens (992px - 1199px) */
@media (max-width: 1199px) and (min-width: 992px) {
    .main-content {
        grid-template-columns: 200px 1fr 280px;
        gap: 1.5rem;
    }

    .dashboard-container {
        padding: 2rem;
    }

    .dashboard-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.75rem;
    }

    .dashboard-button {
        min-height: 180px;
        padding: 1.75rem;
    }

    .button-icon {
        font-size: 2.75rem;
    }

    .quick-stats {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.25rem;
    }
}

/* Large Tablet Styles (992px - 1024px) */
@media (max-width: 1024px) and (min-width: 992px) {
    .dashboard-container {
        padding: 1.75rem;
    }

    .dashboard-title {
        font-size: 2.3rem;
    }

    .dashboard-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }

    .dashboard-button {
        min-height: 180px;
        padding: 1.75rem;
    }

    .button-icon {
        font-size: 2.75rem;
    }

    .quick-stats {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.25rem;
    }
}

/* Medium Tablet Styles (768px - 991px) */
@media (max-width: 991px) and (min-width: 768px) {
    .dashboard-container {
        padding: 1.5rem;
    }

    .dashboard-title {
        font-size: 2.1rem;
    }

    /* Tablet Dashboard Grid - 3x2 layout */
    .dashboard-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.25rem;
    }

    .dashboard-button {
        min-height: 160px;
        padding: 1.5rem;
    }

    .button-icon {
        font-size: 2.5rem;
        margin-bottom: 0.75rem;
    }

    .button-content h3 {
        font-size: 1.2rem;
    }

    .button-content p {
        font-size: 0.85rem;
    }

    .quick-stats {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
    }

    .stat-card {
        padding: 1.25rem;
    }

    /* Email tablet styles */
    .email-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .template-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .template-card {
        padding: 1.25rem;
    }

    .template-card i {
        font-size: 2.25rem;
    }
}

@media (max-width: 992px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .sidebar,
    .calculator {
        position: static;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }
}

/* CLEAN MOBILE STYLES - CONSOLIDATED */
@media (max-width: 768px) {
    /* Basic mobile layout */
    body {
        font-size: 16px; /* Prevents zoom on iOS */
    }

    /* Header mobile optimization */
    .header {
        padding: 0.75rem 0;
    }

    .header-content {
        flex-direction: column;
        gap: 0.75rem;
        text-align: center;
        padding: 0 1rem;
    }

    .logo-section {
        justify-content: center;
    }

    .logo-image {
        height: 36px;
    }

    .logo-section h1 {
        font-size: 1.3rem;
    }

    .logo-section .subtitle {
        font-size: 0.8rem;
    }

    /* Hide main navigation on mobile - use bottom nav instead */
    .main-nav.desktop-nav {
        display: none;
    }

    /* Show mobile navigation header */
    .mobile-nav-header {
        display: flex;
    }

    /* Dashboard Mobile Styles */
    .dashboard-container {
        padding: 1rem;
    }

    .dashboard-title {
        font-size: 1.8rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .dashboard-logo {
        height: 36px;
    }

    .dashboard-subtitle {
        font-size: 1rem;
    }

    /* Mobile Dashboard Grid - 2x3 layout */
    .dashboard-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .dashboard-button {
        min-height: 150px;
        padding: 1.25rem 1rem;
        border-radius: 12px;
    }

    .dashboard-button:hover {
        transform: translateY(-2px) scale(1.01);
    }

    .button-icon {
        font-size: 2.25rem;
        margin-bottom: 0.75rem;
    }

    .button-content h3 {
        font-size: 1rem;
        margin-bottom: 0.25rem;
    }

    .button-content p {
        font-size: 0.75rem;
        line-height: 1.3;
    }

    /* Mobile Quick Stats */
    .quick-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    /* Email Mobile Styles */
    .email-layout {
        padding: 1rem;
    }

    .email-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .email-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .template-grid {
        grid-template-columns: 1fr;
    }

    .template-card {
        padding: 1.25rem;
    }

    .template-card i {
        font-size: 2rem;
    }

    /* Mobile Menu Styles */
    .mobile-menu-overlay.show {
        display: block;
    }

    .mobile-menu.show {
        display: block;
    }

    /* Section Mobile Styles */
    .clients-layout,
    .services-layout,
    .settings-layout,
    .email-layout {
        padding: 1rem;
    }

    .clients-header,
    .services-header,
    .settings-header,
    .email-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .clients-actions,
    .services-actions {
        justify-content: center;
    }

    .clients-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .clients-filters {
        flex-direction: column;
        gap: 1rem;
    }

    .filter-group input,
    .filter-group select {
        min-width: auto;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    /* Cleaning/Orders Mobile */
    .cleaning-navigation {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .cleaning-nav-sidebar {
        flex-direction: row;
        overflow-x: auto;
        padding: 0.5rem;
        gap: 0.5rem;
    }

    .cleaning-nav-tab {
        white-space: nowrap;
        min-width: 120px;
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
    }

    .cleaning-view {
        padding: 1rem;
    }

    .overview-stats {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .week-days {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .task-actions {
        flex-direction: column;
        gap: 0.75rem;
    }

    .task-actions .btn {
        width: 100%;
    }

    .header-actions {
        justify-content: center;
        gap: 0.5rem;
    }

    /* Main content mobile */
    .main-content {
        padding: 0.5rem;
        gap: 1rem;
        margin-bottom: 80px; /* Space for bottom nav */
    }

    /* Mobile layout adjustments */
    .form-area,
    .sidebar,
    .calculator {
        padding: 1rem;
        width: 100%;
        margin-bottom: 1rem;
    }

    .calculator {
        position: static;
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    /* Mobile forms */
    .form-grid {
        grid-template-columns: 1fr !important;
        gap: 1rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    /* Mobile inputs - prevent zoom on iOS */
    input, textarea, select {
        font-size: 16px;
        min-height: 44px;
        padding: 0.75rem;
        border-radius: 8px;
        border: 2px solid #ddd;
        width: 100%;
    }

    input:focus, textarea:focus, select:focus {
        border-color: #5e2e60;
        outline: none;
    }

    /* Mobile buttons */
    .btn, button {
        min-height: 44px;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        border-radius: 8px;
        width: 100%;
        margin-bottom: 0.5rem;
    }

    /* Mobile services */
    .service-item {
        margin-bottom: 0.75rem;
    }

    .service-label {
        padding: 0.75rem;
        min-height: 44px;
        display: flex;
        align-items: center;
    }

    /* Mobile modals */
    .modal-content {
        margin: 1rem;
        max-height: calc(100vh - 2rem);
        overflow-y: auto;
        border-radius: 12px;
    }

    /* Logo adjustments */
    .logo-section {
        flex-direction: column;
        gap: 0.5rem;
    }

    .subtitle {
        margin-left: 0;
    }

    .discount-options {
        flex-direction: column;
        gap: 0.5rem;
    }

    .discount-option {
        justify-content: flex-start;
    }

    /* Bottom navigation for mobile */
    .pwa-bottom-nav {
        display: flex !important;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        border-top: 1px solid #e5e7eb;
        padding: 0.5rem;
        justify-content: space-around;
        z-index: 1000;
        box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
    }

    .pwa-bottom-nav .nav-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 0.5rem;
        color: var(--neutral-medium);
        text-decoration: none;
        font-size: 0.7rem;
        min-width: 60px;
        border-radius: 8px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .pwa-bottom-nav .nav-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 50%;
        width: 0;
        height: 2px;
        background: var(--primary-color);
        transition: all 0.3s ease;
        transform: translateX(-50%);
    }

    .pwa-bottom-nav .nav-item.active {
        color: var(--primary-color);
        background: rgba(94, 46, 96, 0.1);
        font-weight: 600;
    }

    .pwa-bottom-nav .nav-item.active::before {
        width: 80%;
    }

    .pwa-bottom-nav .nav-item:hover:not(.active) {
        color: var(--neutral-dark);
        background: rgba(0, 0, 0, 0.05);
    }

    .pwa-bottom-nav .nav-item i {
        font-size: 1.2rem;
        margin-bottom: 0.25rem;
    }

    /* Mobile task view */
    .mobile-task-view {
        display: block;
        background: white;
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .mobile-task-view h3 {
        color: #5e2e60;
        margin-bottom: 1rem;
        text-align: center;
    }

    .mobile-task-item {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 1rem;
        border-left: 4px solid #327881;
    }

    .mobile-task-item.completed {
        border-left-color: #28a745;
        background: #d4edda;
    }

    .mobile-task-item.in-progress {
        border-left-color: #ffc107;
        background: #fff3cd;
    }

    .mobile-task-item.pending {
        border-left-color: #17a2b8;
        background: #d1ecf1;
    }

    .mobile-task-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 600;
    }

    .mobile-task-actions {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
        margin-top: 0.5rem;
    }

    .mobile-task-actions .btn {
        flex: 1;
        padding: 0.5rem;
        font-size: 0.8rem;
    }

    /* Hide desktop elements on mobile */
    .desktop-only {
        display: none !important;
    }

    .mobile-hidden {
        display: none !important;
    }

    /* Mobile tables - convert to cards */
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    table {
        min-width: 600px;
    }

    /* Mobile typography */
    h1 { font-size: 1.5rem; }
    h2 { font-size: 1.3rem; }
    h3 { font-size: 1.1rem; }
    h4 { font-size: 1rem; }

    /* Mobile cards */
    .card, .client-card, .invoice-card, .service-card {
        margin-bottom: 1rem;
        padding: 1rem;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    /* Mobile alerts */
    .alert {
        margin: 0.5rem;
        padding: 1rem;
        border-radius: 8px;
        font-size: 0.9rem;
    }

    /* PWA safe area support */
    .pwa-mode {
        padding-top: env(safe-area-inset-top);
        padding-bottom: env(safe-area-inset-bottom);
        padding-left: env(safe-area-inset-left);
        padding-right: env(safe-area-inset-right);
    }

    .pwa-mode .main-content {
        padding-bottom: 80px;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Loading States */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* NEW DASHBOARD STYLES */
.dashboard-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.welcome-section {
    text-align: center;
    margin-bottom: 3rem;
}

.dashboard-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--neutral-dark);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.dashboard-logo {
    height: 48px;
    width: auto;
    border-radius: 8px;
}

.dashboard-subtitle {
    font-size: 1.2rem;
    color: var(--neutral-medium);
    font-weight: 400;
}

/* Dashboard Grid - Responsive Square Buttons */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-bottom: 3rem;
}

.dashboard-button {
    background: white;
    border: none;
    border-radius: 16px;
    padding: 2rem;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.dashboard-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-color);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.dashboard-button:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.dashboard-button:hover::before {
    transform: scaleX(1);
}

.dashboard-button:active {
    transform: translateY(-2px) scale(0.98);
}

.button-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    filter: grayscale(0.2);
}

.button-content h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--neutral-dark);
    margin-bottom: 0.5rem;
}

.button-content p {
    font-size: 0.9rem;
    color: var(--neutral-medium);
    line-height: 1.4;
}

/* Individual Button Colors */
.pricing-button::before { background: var(--secondary-color); }
.pricing-button:hover { border-color: var(--secondary-color); }

.invoices-button::before { background: var(--accent-color); }
.invoices-button:hover { border-color: var(--accent-color); }

.orders-button::before { background: var(--primary-color); }
.orders-button:hover { border-color: var(--primary-color); }

.settings-button::before { background: var(--neutral-medium); }
.settings-button:hover { border-color: var(--neutral-medium); }

.email-button::before { background: #dc2626; }
.email-button:hover { border-color: #dc2626; }

.stats-button::before { background: var(--teal-accent); }
.stats-button:hover { border-color: var(--teal-accent); }

/* Quick Stats */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-icon {
    font-size: 2rem;
    opacity: 0.8;
}

.stat-info {
    display: flex;
    flex-direction: column;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--neutral-dark);
}

.stat-label {
    font-size: 0.9rem;
    color: var(--neutral-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* SECTION LAYOUTS - UPDATED DESIGN SYSTEM */

/* Clients Section */
.clients-layout {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.clients-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e5e7eb;
}

.clients-header h2 {
    color: var(--neutral-dark);
    font-size: 1.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.clients-actions {
    display: flex;
    gap: 1rem;
}

.clients-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.clients-stats .stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border-left: 4px solid var(--primary-color);
    transition: transform 0.2s ease;
}

.clients-stats .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.clients-stats .stat-card:nth-child(2) {
    border-left-color: var(--secondary-color);
}

.clients-stats .stat-card:nth-child(3) {
    border-left-color: var(--accent-color);
}

.stat-icon {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-content h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--neutral-dark);
    margin-bottom: 0.25rem;
}

.stat-content p {
    font-size: 0.9rem;
    color: var(--neutral-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.clients-filters {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 500;
    color: var(--neutral-dark);
    font-size: 0.9rem;
}

.filter-group input,
.filter-group select {
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: border-color 0.2s ease;
    min-width: 200px;
}

.filter-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.clients-list {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    min-height: 400px;
}

/* Dashboard Styles - Legacy (keeping for compatibility) */
.dashboard {
    margin-bottom: 2rem;
}

.dashboard h2 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.dashboard-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-left: 4px solid var(--teal-accent);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Orders/Cleaning Section */
.cleaning-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.cleaning-nav-sidebar {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.cleaning-nav-tab {
    background: transparent;
    border: 2px solid #e5e7eb;
    color: var(--neutral-medium);
    padding: 0.75rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cleaning-nav-tab:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
}

.cleaning-nav-tab.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.cleaning-quick-actions {
    display: flex;
    gap: 1rem;
}

.cleaning-view {
    display: none;
    padding: 2rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    margin-bottom: 2rem;
}

.cleaning-view.active {
    display: block;
}

.cleaning-dashboard-layout {
    display: grid;
    gap: 2rem;
}

.dashboard-section h2 {
    color: var(--neutral-dark);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.monthly-overview-card,
.today-tasks-card,
.weekly-overview-card {
    background: #f8fafc;
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
}

.overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: white;
    border-radius: 8px;
    border-left: 3px solid var(--primary-color);
}

.stat-label {
    font-size: 0.9rem;
    color: var(--neutral-medium);
    font-weight: 500;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--neutral-dark);
}

.stat-value.unpaid {
    color: #dc2626;
}

.task-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
    justify-content: center;
}

.week-days {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.week-day {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: white;
    border-radius: 8px;
    border-left: 3px solid var(--secondary-color);
}

.day-label {
    font-size: 0.9rem;
    color: var(--neutral-medium);
    font-weight: 500;
}

.day-count {
    font-size: 1rem;
    font-weight: 600;
    color: var(--neutral-dark);
}

/* Services Section */
.services-layout {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.services-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e5e7eb;
}

.services-header h2 {
    color: var(--neutral-dark);
    font-size: 1.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.services-actions {
    display: flex;
    gap: 1rem;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.service-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
}

.service-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

.service-card h3 {
    color: var(--neutral-dark);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.service-card p {
    color: var(--neutral-medium);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.service-price {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* Settings Section */
.settings-layout {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

.settings-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e5e7eb;
}

.settings-header h2 {
    color: var(--neutral-dark);
    font-size: 1.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.settings-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    margin-bottom: 2rem;
}

.settings-section h3 {
    color: var(--neutral-dark);
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-info h4 {
    color: var(--neutral-dark);
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.setting-info p {
    color: var(--neutral-medium);
    font-size: 0.9rem;
}

.setting-control {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Form Elements */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-weight: 500;
    color: var(--neutral-dark);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: border-color 0.2s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.form-grid .full-width {
    grid-column: 1 / -1;
}

/* EMAIL SECTION STYLES */
.email-layout {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.email-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e5e7eb;
}

.email-header h2 {
    color: var(--neutral-dark);
    font-size: 1.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.email-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.email-templates h3,
.email-history h3 {
    color: var(--neutral-dark);
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.template-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.template-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 2px solid transparent;
}

.template-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

.template-card i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.template-card h4 {
    color: var(--neutral-dark);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.template-card p {
    color: var(--neutral-medium);
    font-size: 0.9rem;
    line-height: 1.4;
}

.email-list {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    min-height: 300px;
}

.no-emails {
    text-align: center;
    color: var(--neutral-medium);
    font-style: italic;
    padding: 2rem;
}

.dashboard-card .card-icon {
    font-size: 2rem;
    color: #5e2e60;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(94, 46, 96, 0.1);
    border-radius: 50%;
}

.dashboard-card .card-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #5e2e60;
    margin: 0;
}

.dashboard-card .card-content p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

/* Orders Management */
.orders-management {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.orders-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.orders-header h3 {
    color: #5e2e60;
    margin: 0;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.orders-actions {
    display: flex;
    gap: 1rem;
}

/* Filters */
.filters {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 500;
    color: #333;
    font-size: 0.9rem;
}

.filter-group input,
.filter-group select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
}

.filter-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: #5e2e60;
    box-shadow: 0 0 0 3px rgba(94, 46, 96, 0.1);
}

/* Tasks */
.tasks-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.tasks-section h4 {
    color: #5e2e60;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tasks-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.task-item {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.task-item:hover {
    border-color: #327881;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.task-item.completed {
    opacity: 0.7;
    background: #f8f9fa;
}

.task-checkbox {
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.task-content {
    flex: 1;
}

.task-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.task-details {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: #666;
}

.task-detail {
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.task-actions {
    display: flex;
    gap: 0.5rem;
}

.task-actions .btn {
    padding: 0.5rem;
    font-size: 0.8rem;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-content.large {
    max-width: 900px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
    margin: 0;
    color: #5e2e60;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.close {
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
    transition: color 0.3s ease;
}

.close:hover {
    color: #333;
}

.modal-body {
    padding: 1.5rem;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
}

/* Calendar Styles */
.calendar-container {
    width: 100%;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.calendar-header h4 {
    margin: 0;
    color: #5e2e60;
    font-size: 1.2rem;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.calendar-day {
    background: white;
    padding: 1rem;
    min-height: 80px;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    position: relative;
}

.calendar-day.other-month {
    background: #f8f9fa;
    color: #999;
}

.calendar-day.today {
    background: rgba(94, 46, 96, 0.1);
    border: 2px solid #5e2e60;
}

.calendar-day.has-tasks {
    background: rgba(50, 120, 129, 0.1);
}

.calendar-day-number {
    font-weight: 600;
    font-size: 0.9rem;
}

.calendar-task {
    background: #327881;
    color: white;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-size: 0.7rem;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

/* Settings Styles */
.settings-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.settings-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.settings-section h3 {
    color: #5e2e60;
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-item label {
    font-weight: 500;
    color: #333;
}

.setting-item input,
.setting-item select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    min-width: 200px;
}

.setting-item input[type="checkbox"] {
    min-width: auto;
    width: 20px;
    height: 20px;
}

/* Enhanced Task Styles */
.task-item {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    position: relative;
}

.task-item:hover {
    border-color: #327881;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.task-item.task-pending {
    border-left: 4px solid #ffc107;
}

.task-item.task-in_progress {
    border-left: 4px solid #007bff;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.05) 0%, rgba(255, 255, 255, 1) 100%);
}

.task-item.task-completed {
    border-left: 4px solid #28a745;
    background: rgba(40, 167, 69, 0.05);
}

.task-item.task-cancelled {
    border-left: 4px solid #dc3545;
    opacity: 0.7;
}

.task-status-indicator {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.status-icon {
    font-size: 1.5rem;
    width: 40px;
    text-align: center;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #327881 0%, #5e2e60 100%);
    transition: width 0.3s ease;
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.task-title {
    font-weight: 600;
    color: #333;
    font-size: 1.1rem;
}

.task-status {
    background: #f8f9fa;
    color: #5e2e60;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.task-progress-steps {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e0e0e0;
}

.step {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0;
    color: #666;
    font-size: 0.9rem;
}

.step.completed {
    color: #28a745;
    font-weight: 500;
}

.step i {
    width: 20px;
    text-align: center;
}

/* Task Progress Modal Styles */
.task-progress-content {
    max-width: 600px;
}

.task-info {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
}

.task-info h4 {
    color: #5e2e60;
    margin-bottom: 0.5rem;
}

.task-info p {
    margin: 0.25rem 0;
    color: #666;
}

.progress-steps {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.step-item {
    display: flex;
    gap: 1rem;
    padding: 1.5rem;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.step-item.active {
    border-color: #327881;
    background: rgba(50, 120, 129, 0.05);
}

.step-item.completed {
    border-color: #28a745;
    background: rgba(40, 167, 69, 0.05);
}

.step-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    color: #666;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.step-item.active .step-icon {
    background: #327881;
    color: white;
}

.step-item.completed .step-icon {
    background: #28a745;
    color: white;
}

.step-content {
    flex: 1;
}

.step-content h5 {
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.step-content p {
    color: #666;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.completed-time {
    color: #28a745;
    font-weight: 500;
    font-size: 0.9rem;
}

.completion-section {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 2px solid #e0e0e0;
}

.completion-section h5 {
    color: #5e2e60;
    margin-bottom: 1rem;
}

.completion-section textarea {
    width: 100%;
    padding: 1rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-family: inherit;
    resize: vertical;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    width: 100%;
}

/* Completion Modal Styles */
.completion-summary {
    text-align: center;
    max-width: 500px;
}

.completion-header {
    margin-bottom: 2rem;
}

.completion-icon {
    font-size: 4rem;
    color: #28a745;
    margin-bottom: 1rem;
}

.completion-details {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    text-align: left;
}

.completion-details h4 {
    color: #5e2e60;
    margin-bottom: 1rem;
}

.completion-notes {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e0e0e0;
}

.completion-notes p {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    margin-top: 0.5rem;
    font-style: italic;
}

.photo-summary {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e0e0e0;
}

.photo-summary span {
    background: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
}

.completion-actions {
    display: flex;
    gap: 1rem;
    flex-direction: column;
}

/* Photo Viewer Styles */
.photos-viewer h4 {
    color: #5e2e60;
    margin-bottom: 1.5rem;
    text-align: center;
}

.photos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.photo-item {
    text-align: center;
}

.photo-preview {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.photo-timestamp {
    margin-top: 0.5rem;
    font-size: 0.8rem;
    color: #666;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 1rem 1.5rem;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 300px;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    border-left: 4px solid #28a745;
}

.notification-info {
    border-left: 4px solid #007bff;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.notification-content i {
    color: #28a745;
}

.notification-info .notification-content i {
    color: #007bff;
}

/* No Tasks State */
.no-tasks {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    padding: 2rem;
}

.no-tasks-content {
    text-align: center;
    color: #666;
}

.no-tasks-content i {
    font-size: 3rem;
    color: #ddd;
    margin-bottom: 1rem;
}

.no-tasks-content h4 {
    color: #999;
    margin-bottom: 0.5rem;
}

.no-tasks-content p {
    margin-bottom: 1.5rem;
    color: #999;
}

/* Ensure tasks sections are always visible */
.tasks-container {
    min-height: 400px;
}

.tasks-section {
    margin-bottom: 2rem;
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tasks-section h4 {
    color: #5e2e60;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #f0f0f0;
}

.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Orders List Styles */
.orders-list-container {
    padding: 1rem 0;
}

.orders-list-header {
    margin-bottom: 1.5rem;
}

.orders-search {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.orders-search input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
}

.orders-list-content {
    max-height: 500px;
    overflow-y: auto;
}

.order-item {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.order-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.order-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.25rem;
}

.order-id {
    font-size: 0.85rem;
    color: #666;
    font-family: monospace;
}

.order-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
}

.order-status.active {
    background-color: #d4edda;
    color: #155724;
}

.order-status.completed {
    background-color: #cce5ff;
    color: #004085;
}

.order-status.cancelled {
    background-color: #f8d7da;
    color: #721c24;
}

.order-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.order-detail {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #555;
}

.order-detail i {
    color: #327881;
    width: 16px;
}

.order-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.order-actions .btn {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
}

.no-orders {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.no-orders i {
    font-size: 3rem;
    color: #ccc;
    margin-bottom: 1rem;
}

.no-orders h4 {
    margin-bottom: 0.5rem;
    color: #333;
}

/* CRM and Invoicing Styles */

/* Clients Layout */
.clients-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.clients-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.clients-header h2 {
    color: #5e2e60;
    margin: 0;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.clients-actions {
    display: flex;
    gap: 1rem;
}

.clients-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #327881;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.stat-card .stat-icon {
    font-size: 2rem;
    color: #5e2e60;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(94, 46, 96, 0.1);
    border-radius: 50%;
}

.stat-card .stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #5e2e60;
    margin: 0;
}

.stat-card .stat-content p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.clients-filters {
    display: flex;
    gap: 2rem;
    align-items: center;
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
}

.clients-filters .filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.clients-filters .filter-group label {
    font-weight: 500;
    color: #333;
    font-size: 0.9rem;
}

.clients-filters .filter-group input,
.clients-filters .filter-group select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
    min-width: 200px;
}

.clients-filters .filter-group input:focus,
.clients-filters .filter-group select:focus {
    outline: none;
    border-color: #5e2e60;
    box-shadow: 0 0 0 3px rgba(94, 46, 96, 0.1);
}

/* Client Items */
.clients-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.client-item {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.client-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: #327881;
}

.client-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.client-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #5e2e60 0%, #4a1e4a 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: 600;
}

.client-info {
    flex: 1;
}

.client-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin: 0 0 0.25rem 0;
}

.client-contact {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.25rem;
}

.client-contact span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.client-contact i {
    color: #327881;
    width: 14px;
}

.client-address {
    font-size: 0.9rem;
    color: #666;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.client-address i {
    color: #327881;
    width: 14px;
}

.client-stats {
    display: flex;
    gap: 1rem;
}

.client-stats .stat {
    text-align: center;
}

.client-stats .stat-value {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    color: #5e2e60;
}

.client-stats .stat-label {
    font-size: 0.8rem;
    color: #666;
}

.client-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
}

/* No clients state */
.no-clients {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.no-clients i {
    font-size: 3rem;
    color: #ddd;
    margin-bottom: 1rem;
}

.no-clients h4 {
    margin-bottom: 0.5rem;
    color: #333;
}

/* Invoices Layout */
.invoices-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.invoices-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.invoices-header h2 {
    color: #5e2e60;
    margin: 0;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.invoices-actions {
    display: flex;
    gap: 1rem;
}

.invoices-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.invoices-filters {
    display: flex;
    gap: 2rem;
    align-items: center;
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
}

.invoices-filters .filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.invoices-filters .filter-group label {
    font-weight: 500;
    color: #333;
    font-size: 0.9rem;
}

.invoices-filters .filter-group input,
.invoices-filters .filter-group select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
    min-width: 200px;
}

.invoices-filters .filter-group input:focus,
.invoices-filters .filter-group select:focus {
    outline: none;
    border-color: #5e2e60;
    box-shadow: 0 0 0 3px rgba(94, 46, 96, 0.1);
}

/* Invoice Items */
.invoices-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Invoice Items Header */
.invoice-items-header {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.invoice-items-header .btn-sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
}

/* Available Services Modal */
.service-category-section {
    margin-bottom: 2rem;
}

.category-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #5e2e60;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #5e2e60;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.service-card {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.service-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.service-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.service-header h4 {
    margin: 0;
    font-size: 0.9rem;
    color: #333;
    flex: 1;
}

.service-price {
    font-weight: bold;
    color: #5e2e60;
    font-size: 0.9rem;
}

.service-period {
    font-size: 0.8rem;
    color: #666;
    font-style: italic;
    margin: 0.25rem 0;
}

.service-actions {
    text-align: right;
}

/* No items message */
.no-items {
    text-align: center;
    padding: 2rem;
    color: #666;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px dashed #ddd;
}

.invoice-item {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.invoice-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.invoice-item.invoice-paid {
    border-left: 4px solid #28a745;
    background: rgba(40, 167, 69, 0.02);
}

.invoice-item.invoice-pending {
    border-left: 4px solid #ffc107;
    background: rgba(255, 193, 7, 0.02);
}

.invoice-item.invoice-overdue {
    border-left: 4px solid #dc3545;
    background: rgba(220, 53, 69, 0.02);
}

.invoice-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.invoice-number {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #5e2e60;
}

.invoice-number i {
    color: #327881;
}

.invoice-number .number {
    font-family: monospace;
    font-size: 1.1rem;
}

.invoice-client {
    flex: 1;
}

.invoice-client h4 {
    margin: 0 0 0.25rem 0;
    font-size: 1.1rem;
    color: #333;
}

.invoice-date {
    font-size: 0.9rem;
    color: #666;
}

.invoice-amount {
    text-align: right;
}

.invoice-amount .amount {
    display: block;
    font-size: 1.2rem;
    font-weight: 600;
    color: #5e2e60;
}

.invoice-amount .status {
    font-size: 0.8rem;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-weight: 500;
    text-transform: uppercase;
    margin-top: 0.25rem;
    display: inline-block;
}

.invoice-amount .status.paid {
    background-color: #d4edda;
    color: #155724;
}

.invoice-amount .status.pending {
    background-color: #fff3cd;
    color: #856404;
}

.invoice-amount .status.overdue {
    background-color: #f8d7da;
    color: #721c24;
}

.invoice-details {
    display: flex;
    gap: 2rem;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: #666;
}

.invoice-details .detail-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.invoice-details .detail-item i {
    color: #327881;
    width: 16px;
}

.invoice-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

/* No invoices state */
.no-invoices {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.no-invoices i {
    font-size: 3rem;
    color: #ddd;
    margin-bottom: 1rem;
}

.no-invoices h4 {
    margin-bottom: 0.5rem;
    color: #333;
}

/* Services Layout */
.services-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.services-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.services-header h2 {
    color: #5e2e60;
    margin: 0;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.services-actions {
    display: flex;
    gap: 1rem;
}

.services-categories {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.service-category-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.service-category-section h3 {
    color: #5e2e60;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #9f7ba0;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
}

.service-edit-item {
    background: #f8f9fa;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 1rem;
    transition: border-color 0.3s ease;
}

.service-edit-item:hover {
    border-color: #327881;
}

.service-edit-item .service-name {
    font-weight: 500;
    color: #333;
    margin-bottom: 0.5rem;
}

.service-edit-item .service-price-input {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.service-edit-item input {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

.service-edit-item input:focus {
    outline: none;
    border-color: #5e2e60;
    box-shadow: 0 0 0 3px rgba(94, 46, 96, 0.1);
}

.service-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.service-actions {
    display: flex;
    gap: 0.25rem;
}

.service-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.service-description {
    font-size: 0.85rem;
    color: #666;
    margin-top: 0.5rem;
    font-style: italic;
}

/* Large Modal for Invoice Form */
.large-modal .modal-content {
    max-width: 900px;
    width: 95%;
}

.invoice-form-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.invoice-basic-info,
.invoice-items-section,
.invoice-summary,
.invoice-notes {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
}

.invoice-basic-info h4,
.invoice-items-section h4,
.invoice-summary h4,
.invoice-notes h4 {
    color: #5e2e60;
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
}

.invoice-items-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.invoice-items-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.invoice-item-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr auto;
    gap: 1rem;
    align-items: center;
    background: white;
    padding: 1rem;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

.invoice-item-row input,
.invoice-item-row select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

.invoice-item-row input:focus,
.invoice-item-row select:focus {
    outline: none;
    border-color: #5e2e60;
    box-shadow: 0 0 0 3px rgba(94, 46, 96, 0.1);
}

.invoice-item-row .remove-item {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.5rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.invoice-item-row .remove-item:hover {
    background: #c82333;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e0e0e0;
}

.summary-row.total {
    font-size: 1.1rem;
    font-weight: 600;
    color: #5e2e60;
    border-top: 2px solid #5e2e60;
    border-bottom: none;
    padding-top: 1rem;
    margin-top: 0.5rem;
}

.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e0e0e0;
}

/* Responsive adjustments for CRM */
@media (max-width: 768px) {
    .clients-header,
    .invoices-header,
    .services-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .clients-actions,
    .invoices-actions,
    .services-actions {
        justify-content: center;
    }

    .clients-filters,
    .invoices-filters {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .clients-filters .filter-group input,
    .clients-filters .filter-group select,
    .invoices-filters .filter-group input,
    .invoices-filters .filter-group select {
        min-width: auto;
    }

    .client-header {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .client-contact {
        flex-direction: column;
        gap: 0.25rem;
    }

    .client-stats {
        justify-content: center;
    }

    .invoice-header {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .invoice-details {
        flex-direction: column;
        gap: 0.5rem;
    }

    .invoice-actions,
    .client-actions {
        justify-content: center;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .invoice-item-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .large-modal .modal-content {
        width: 98%;
        margin: 1rem;
    }
}

/* Invoiced badge for tasks */
.invoiced-badge {
    background: #28a745;
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-left: 0.5rem;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.invoiced-badge i {
    font-size: 0.7rem;
}

/* Bulk Invoice Modal Styles */
.bulk-invoice-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.bulk-invoice-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e0e0e0;
}

.bulk-invoice-header h4 {
    color: #5e2e60;
    margin: 0;
}

.bulk-actions {
    display: flex;
    gap: 0.5rem;
}

.bulk-orders-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
}

.bulk-order-item {
    border-bottom: 1px solid #f0f0f0;
}

.bulk-order-item:last-child {
    border-bottom: none;
}

.bulk-order-label {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.bulk-order-label:hover {
    background-color: #f8f9fa;
}

.bulk-order-checkbox {
    width: 18px;
    height: 18px;
    accent-color: #5e2e60;
}

.bulk-order-info {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 1rem;
    align-items: center;
}

.bulk-order-info .order-header {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.bulk-order-info .order-id {
    font-weight: 600;
    color: #5e2e60;
    font-family: monospace;
}

.bulk-order-info .order-date {
    font-size: 0.85rem;
    color: #666;
}

.bulk-order-info .order-customer {
    font-weight: 500;
    color: #333;
}

.bulk-order-info .order-service {
    color: #666;
    font-size: 0.9rem;
}

.bulk-order-info .order-total {
    font-weight: 600;
    color: #327881;
    text-align: right;
}

.bulk-summary {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.summary-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 1rem;
}

.summary-info strong {
    color: #5e2e60;
}

/* Invoice Detail Modal Styles */
.invoice-detail-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.invoice-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, #5e2e60 0%, #4a1e4a 100%);
    color: white;
    border-radius: 8px;
}

.invoice-number-large {
    font-size: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.invoice-status-large {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.9rem;
}

.invoice-status-large.paid {
    background-color: #28a745;
    color: white;
}

.invoice-status-large.pending {
    background-color: #ffc107;
    color: #212529;
}

.invoice-status-large.overdue {
    background-color: #dc3545;
    color: white;
}

.invoice-detail-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.info-section h4 {
    color: #5e2e60;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.info-grid {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.info-item label {
    font-weight: 500;
    color: #666;
}

.info-item span {
    color: #333;
    font-weight: 500;
}

.client-info {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
}

.client-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.client-contact,
.client-address {
    color: #666;
    margin-bottom: 0.25rem;
}

.invoice-items-detail h4 {
    color: #5e2e60;
    margin-bottom: 1rem;
}

.invoice-items-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.invoice-items-table th {
    background: #f8f9fa;
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: #5e2e60;
    border-bottom: 1px solid #e0e0e0;
}

.invoice-items-table td {
    padding: 1rem;
    border-bottom: 1px solid #f0f0f0;
}

.invoice-items-table tr:last-child td {
    border-bottom: none;
}

.invoice-totals-detail {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.totals-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e0e0e0;
}

.totals-row.total {
    font-size: 1.2rem;
    font-weight: 600;
    color: #5e2e60;
    border-top: 2px solid #5e2e60;
    border-bottom: none;
    padding-top: 1rem;
    margin-top: 0.5rem;
}

.invoice-notes-detail {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.invoice-notes-detail h4 {
    color: #5e2e60;
    margin-bottom: 1rem;
}

.invoice-notes-detail p {
    color: #666;
    line-height: 1.6;
    margin: 0;
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 1rem 1.5rem;
    z-index: 10000;
    opacity: 0;
    transform: translateY(-100%);
    transition: all 0.3s ease;
    max-width: 400px;
    border-left: 4px solid #327881;
}

.notification.notification-success {
    border-left-color: #28a745;
}

.notification.notification-error {
    border-left-color: #dc3545;
}

.notification.notification-warning {
    border-left-color: #ffc107;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.notification-content i {
    font-size: 1.2rem;
    color: #327881;
}

.notification-success .notification-content i {
    color: #28a745;
}

.notification-error .notification-content i {
    color: #dc3545;
}

.notification-warning .notification-content i {
    color: #ffc107;
}

.notification-content span {
    color: #333;
    font-weight: 500;
}

/* Responsive adjustments for notifications */
@media (max-width: 768px) {
    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }
}

/* Advanced Statistics Styles */
.advanced-stats-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.stats-section {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.stats-section h4 {
    color: #5e2e60;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stats-section h5 {
    color: #333;
    margin: 1.5rem 0 1rem 0;
    font-size: 1rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 1rem;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

.stat-label {
    color: #666;
    font-weight: 500;
}

.stat-value {
    color: #5e2e60;
    font-weight: 600;
    font-size: 1.1rem;
}

.top-clients-list,
.popular-services-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.top-client-item,
.popular-service-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

.client-name,
.service-name {
    color: #333;
    font-weight: 500;
}

.client-revenue,
.service-revenue {
    color: #327881;
    font-weight: 600;
}

.service-rank {
    color: #5e2e60;
    font-weight: 600;
    margin-right: 0.5rem;
    min-width: 20px;
}

/* Settings Styles */
.settings-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.settings-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.settings-section {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.settings-section h3 {
    color: #5e2e60;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #9f7ba0;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-item label {
    color: #333;
    font-weight: 500;
    flex: 1;
}

.setting-item input,
.setting-item select {
    max-width: 200px;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
}

.setting-item input:focus,
.setting-item select:focus {
    outline: none;
    border-color: #5e2e60;
    box-shadow: 0 0 0 3px rgba(94, 46, 96, 0.1);
}

.settings-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.settings-actions .btn {
    flex: 1;
    min-width: 150px;
}

/* Responsive adjustments for advanced stats */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .top-client-item,
    .popular-service-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .settings-actions {
        flex-direction: column;
    }

    .settings-actions .btn {
        min-width: auto;
    }

    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .setting-item input,
    .setting-item select {
        max-width: none;
        width: 100%;
    }
}

/* ===== SIMPLIFIED GRAVE CLEANING SYSTEM STYLES ===== */

/* Cleaning Navigation */
.cleaning-navigation {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.cleaning-nav-sidebar {
    background: white;
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 200px;
}

.cleaning-nav-tab {
    padding: 0.75rem 1rem;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    color: #495057;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-align: left;
    width: 100%;
}

.cleaning-nav-tab:hover {
    background: #e9ecef;
    color: #327881;
    transform: translateX(4px);
}

.cleaning-nav-tab.active {
    background: linear-gradient(135deg, #4a1e4a 0%, #5e2e60 100%);
    color: white;
    border-color: #4a1e4a;
    box-shadow: 0 4px 12px rgba(74, 30, 74, 0.3);
}

.cleaning-nav-tab i {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

/* Global Search */
.global-search {
    flex: 1;
    max-width: 400px;
}

.search-container {
    position: relative;
}

.search-container i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

#globalSearch {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

#globalSearch:focus {
    outline: none;
    border-color: #5e2e60;
    box-shadow: 0 0 0 3px rgba(94, 46, 96, 0.1);
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.search-suggestion {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background 0.2s ease;
}

.search-suggestion:hover {
    background: #f8f9fa;
}

.search-suggestion:last-child {
    border-bottom: none;
}

/* Quick Actions */
.cleaning-quick-actions {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
    padding-top: 1rem;
}

/* Cleaning Views */
.cleaning-view {
    display: none;
    flex: 1;
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.cleaning-view.active {
    display: block;
}

/* Dashboard Styles */
.cleaning-dashboard-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.dashboard-section {
    margin-bottom: 2rem;
}

.dashboard-section h2,
.dashboard-section h3 {
    color: #5e2e60;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.monthly-overview-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 2rem;
    border: 2px solid #327881;
}

.overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
}

.stat-label {
    font-weight: 500;
    color: #495057;
}

.stat-value {
    font-weight: 700;
    font-size: 1.1rem;
    color: #327881;
}

.stat-value.unpaid {
    color: #dc3545;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

/* Enhanced Metrics Cards */
.metric-card {
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #5e2e60 0%, #327881 100%);
}

.trend-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
    font-size: 0.8rem;
}

.trend-value {
    font-weight: 600;
    color: #28a745;
}

.trend-value.negative {
    color: #dc3545;
}

.trend-period {
    color: #666;
}

.priority-breakdown {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.priority-high,
.priority-medium,
.priority-low {
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
}

.priority-high {
    background: #dc3545;
    color: white;
}

.priority-medium {
    background: #ffc107;
    color: #333;
}

.priority-low {
    background: #28a745;
    color: white;
}

.completion-rate {
    margin-top: 0.5rem;
    font-size: 0.9rem;
    color: #666;
}

.conversion-details {
    margin-top: 0.5rem;
    font-size: 0.8rem;
    color: #666;
}

/* Mini Kanban Board */
.mini-kanban {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
}

.kanban-column {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    min-height: 200px;
}

.kanban-column h4 {
    color: #5e2e60;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.mini-kanban-tasks {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.mini-task-card {
    background: white;
    padding: 0.75rem;
    border-radius: 6px;
    border-left: 3px solid #327881;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.mini-task-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Upcoming Deadlines */
.upcoming-deadlines {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.deadline-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #ffc107;
}

.deadline-item.overdue {
    border-left-color: #dc3545;
    background: rgba(220, 53, 69, 0.05);
}

.deadline-item.today {
    border-left-color: #007bff;
    background: rgba(0, 123, 255, 0.05);
}

.deadline-date {
    font-weight: 600;
    color: #5e2e60;
    min-width: 80px;
}

.deadline-content {
    flex: 1;
}

.deadline-title {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.deadline-description {
    font-size: 0.8rem;
    color: #666;
}

/* Activity Feed */
.activity-feed {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #5e2e60;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.activity-description {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.8rem;
    color: #999;
}

/* Contacts Layout */
.contacts-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contacts-header {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.contacts-header h2 {
    color: #5e2e60;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.contacts-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Contacts Filters */
.contacts-filters {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 150px;
}

.filter-group label {
    font-size: 0.9rem;
    font-weight: 500;
    color: #333;
}

.filter-group input,
.filter-group select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
}

.filter-actions {
    display: flex;
    align-items: end;
}

/* Contacts Table */
.contacts-table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table-controls {
    padding: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.bulk-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.bulk-actions label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.table-info {
    font-size: 0.9rem;
    color: #666;
}

.contacts-table {
    width: 100%;
    border-collapse: collapse;
}

.contacts-table th {
    background: #f8f9fa;
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: #5e2e60;
    border-bottom: 1px solid #e0e0e0;
    cursor: pointer;
    transition: background 0.2s ease;
}

.contacts-table th:hover {
    background: #e9ecef;
}

.contacts-table th.sortable {
    position: relative;
}

.sort-icon {
    margin-left: 0.5rem;
    opacity: 0.5;
}

.contacts-table th.sorted .sort-icon {
    opacity: 1;
}

.contacts-table td {
    padding: 1rem;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
}

.contacts-table tr:hover {
    background: #f8f9fa;
}

.contact-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #5e2e60;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-right: 1rem;
}

.contact-info {
    display: flex;
    align-items: center;
}

.contact-name {
    font-weight: 500;
    color: #333;
}

.contact-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.contact-status.lead {
    background: #ffc107;
    color: #333;
}

.contact-status.prospect {
    background: #007bff;
    color: white;
}

.contact-status.customer {
    background: #28a745;
    color: white;
}

.contact-status.inactive {
    background: #6c757d;
    color: white;
}

.table-pagination {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Tasks Layout */
.tasks-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.tasks-header {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.tasks-header h2 {
    color: #5e2e60;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tasks-view-toggle {
    display: flex;
    gap: 0.5rem;
}

.tasks-view-toggle .btn.active {
    background: #5e2e60;
    color: white;
}

.tasks-actions {
    display: flex;
    gap: 1rem;
}

/* Tasks Filters */
.tasks-filters {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
    align-items: end;
}

/* Tasks View Content */
.tasks-view-content {
    display: none;
}

.tasks-view-content.active {
    display: block;
}

/* Kanban Board */
.kanban-board {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.kanban-column {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    min-height: 500px;
    display: flex;
    flex-direction: column;
}

.kanban-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e0e0e0;
}

.kanban-header h3 {
    color: #5e2e60;
    margin: 0;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.task-count {
    background: #5e2e60;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.kanban-tasks {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    min-height: 200px;
    padding: 0.5rem;
    border-radius: 8px;
    transition: background 0.2s ease;
}

.kanban-tasks.drag-over {
    background: rgba(94, 46, 96, 0.1);
}

.kanban-task-card {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: grab;
    transition: all 0.3s ease;
    border-left: 4px solid #327881;
}

.kanban-task-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.kanban-task-card.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.kanban-task-card.high-priority {
    border-left-color: #dc3545;
}

.kanban-task-card.medium-priority {
    border-left-color: #ffc107;
}

.kanban-task-card.low-priority {
    border-left-color: #28a745;
}

.task-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.task-card-title {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
    line-height: 1.3;
}

.task-card-priority {
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
}

.task-card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.75rem;
    font-size: 0.8rem;
    color: #666;
}

.task-card-assignee {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.assignee-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #5e2e60;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: 600;
}

.task-card-deadline {
    font-size: 0.8rem;
}

.task-card-deadline.overdue {
    color: #dc3545;
    font-weight: 600;
}

.task-card-deadline.today {
    color: #007bff;
    font-weight: 600;
}

.task-card-labels {
    display: flex;
    gap: 0.25rem;
    margin-top: 0.5rem;
    flex-wrap: wrap;
}

.task-label {
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
    background: #e9ecef;
    color: #495057;
}

.task-label.urgent {
    background: #dc3545;
    color: white;
}

.task-label.cleaning {
    background: #007bff;
    color: white;
}

.task-label.maintenance {
    background: #28a745;
    color: white;
}

.task-label.follow-up {
    background: #ffc107;
    color: #333;
}

.add-task-btn {
    margin-top: 1rem;
    padding: 0.75rem;
    border: 2px dashed #ccc;
    background: transparent;
    color: #666;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.add-task-btn:hover {
    border-color: #5e2e60;
    color: #5e2e60;
    background: rgba(94, 46, 96, 0.05);
}

/* Pipeline Styles */
.pipeline-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.pipeline-header {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.pipeline-stats {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: #5e2e60;
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
}

.pipeline-filters {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
    align-items: end;
}

.pipeline-board {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 1.5rem;
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow-x: auto;
}

.pipeline-stage {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    min-height: 500px;
    min-width: 250px;
    display: flex;
    flex-direction: column;
}

.stage-header {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e0e0e0;
}

.stage-header h3 {
    color: #5e2e60;
    margin: 0;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stage-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
}

.deals-count {
    background: #5e2e60;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-weight: 500;
}

.stage-value {
    color: #327881;
    font-weight: 600;
}

.stage-deals {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    min-height: 200px;
    padding: 0.5rem;
    border-radius: 8px;
    transition: background 0.2s ease;
}

.pipeline-deal-card {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: grab;
    transition: all 0.3s ease;
    border-left: 4px solid #327881;
}

.pipeline-deal-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.deal-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.deal-title {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
    line-height: 1.3;
}

.deal-value {
    font-weight: 600;
    color: #327881;
    font-size: 0.9rem;
}

.deal-company {
    color: #666;
    font-size: 0.8rem;
    margin-bottom: 0.75rem;
}

.deal-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.deal-probability {
    color: #5e2e60;
    font-weight: 500;
}

.add-deal-btn {
    margin-top: 1rem;
    padding: 0.75rem;
    border: 2px dashed #ccc;
    background: transparent;
    color: #666;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.add-deal-btn:hover {
    border-color: #5e2e60;
    color: #5e2e60;
    background: rgba(94, 46, 96, 0.05);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .crm-navigation {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .crm-nav-tabs {
        justify-content: center;
    }

    .global-search {
        max-width: none;
    }

    .kanban-board {
        grid-template-columns: repeat(2, 1fr);
    }

    .pipeline-board {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .crm-navigation {
        padding: 1rem;
    }

    .crm-nav-tabs {
        flex-wrap: wrap;
        gap: 0.25rem;
    }

    .crm-nav-tab {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }

    .dashboard-cards {
        grid-template-columns: repeat(2, 1fr);
    }

    .mini-kanban {
        grid-template-columns: repeat(2, 1fr);
    }

    .kanban-board {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .pipeline-board {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .contacts-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        min-width: auto;
    }

    .contacts-table {
        font-size: 0.8rem;
    }

    .contacts-table th,
    .contacts-table td {
        padding: 0.5rem;
    }
}

@media (max-width: 480px) {
    .dashboard-cards {
        grid-template-columns: 1fr;
    }

    .mini-kanban {
        grid-template-columns: 1fr;
    }

    .contacts-header {
        flex-direction: column;
        align-items: stretch;
    }

    .contacts-actions {
        justify-content: center;
    }

    .table-controls {
        flex-direction: column;
        gap: 1rem;
    }
}

/* CRM Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    padding: 1rem 1.5rem;
    z-index: 10000;
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.3s ease;
    max-width: 400px;
    border-left: 4px solid #327881;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.notification-success {
    border-left-color: #28a745;
}

.notification.notification-error {
    border-left-color: #dc3545;
}

.notification.notification-warning {
    border-left-color: #ffc107;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.notification-content i {
    font-size: 1.2rem;
    color: #327881;
}

.notification-success .notification-content i {
    color: #28a745;
}

.notification-error .notification-content i {
    color: #dc3545;
}

.notification-warning .notification-content i {
    color: #ffc107;
}

.notification-content span {
    font-size: 0.9rem;
    color: #333;
    line-height: 1.4;
}

/* Enhanced Calendar Styles */
.calendar-layout {
    padding: 1.5rem;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.calendar-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.calendar-controls h3 {
    margin: 0;
    min-width: 150px;
    text-align: center;
    color: #5e2e60;
}

.calendar-view-options {
    display: flex;
    gap: 0.5rem;
}

.calendar-weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: #e0e0e0;
    border-radius: 8px 8px 0 0;
    overflow: hidden;
}

.weekday {
    background: #5e2e60;
    color: white;
    padding: 1rem;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: #e0e0e0;
    border-radius: 0 0 8px 8px;
    overflow: hidden;
}

.calendar-day {
    background: white;
    padding: 0.75rem;
    min-height: 100px;
    display: flex;
    flex-direction: column;
    cursor: pointer;
    transition: all 0.2s ease;
}

.calendar-day:hover {
    background: #f8f9fa;
}

.calendar-day.empty {
    background: #f5f5f5;
    cursor: default;
}

.calendar-day.today {
    background: rgba(94, 46, 96, 0.1);
    border: 2px solid #5e2e60;
}

.calendar-day.has-items {
    background: rgba(50, 120, 129, 0.05);
}

.calendar-day-number {
    font-weight: 600;
    font-size: 1rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.calendar-items {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.calendar-item {
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.calendar-item.task {
    background: #327881;
    color: white;
}

.calendar-item.task.high-priority {
    background: #e74c3c;
}

.calendar-item.task.urgent-priority {
    background: #c0392b;
}

.calendar-item.event {
    background: #5e2e60;
    color: white;
}

.calendar-more {
    font-size: 0.7rem;
    color: #666;
    font-style: italic;
}

/* Notes Styles */
.notes-layout {
    padding: 1.5rem;
}

.notes-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.notes-filters {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.notes-filters .form-group {
    margin: 0;
    min-width: 200px;
}

.notes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.note-card {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.2s ease;
    cursor: pointer;
}

.note-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #5e2e60;
}

.note-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.note-title {
    font-weight: 600;
    font-size: 1.1rem;
    color: #333;
    line-height: 1.3;
    flex: 1;
    margin-right: 1rem;
}

.note-actions {
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.note-card:hover .note-actions {
    opacity: 1;
}

.note-content {
    color: #666;
    line-height: 1.5;
    margin-bottom: 1rem;
    white-space: pre-wrap;
}

.note-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
    color: #888;
    margin-bottom: 1rem;
}

.note-category,
.note-date {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.note-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.note-tag {
    background: #f0f0f0;
    color: #666;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.notes-empty {
    text-align: center;
    padding: 4rem 2rem;
    color: #666;
    background: #f8f9fa;
    border-radius: 12px;
    border: 2px dashed #ddd;
}

.notes-empty i {
    font-size: 3rem;
    color: #ccc;
    margin-bottom: 1rem;
    display: block;
}

.notes-empty h3 {
    margin: 1rem 0;
    color: #333;
}

/* Day Details Modal */
.day-tasks,
.day-events {
    margin-bottom: 1.5rem;
}

.day-item {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.5rem;
}

.day-item.task {
    border-left: 4px solid #327881;
}

.day-item.event {
    border-left: 4px solid #5e2e60;
}

.item-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.item-meta {
    font-size: 0.9rem;
    color: #666;
}

/* Drag and Drop Enhancements */
.kanban-tasks.drag-over {
    background: rgba(94, 46, 96, 0.1);
    border: 2px dashed #5e2e60;
    border-radius: 8px;
}

.kanban-task-card.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

/* No data states */
.no-deadlines,
.no-activity {
    text-align: center;
    padding: 2rem;
    color: #666;
}

.no-deadlines i,
.no-activity i {
    font-size: 2rem;
    color: #ddd;
    margin-bottom: 1rem;
    display: block;
}

/* Enhanced form styles for CRM modals */
.form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: #333;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #5e2e60;
    box-shadow: 0 0 0 3px rgba(94, 46, 96, 0.1);
}

@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }

    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
        transform: translateY(-100px);
    }

    .notification.show {
        transform: translateY(0);
    }
}

/* ===== CLEANING SYSTEM STYLES ===== */

/* Today's Tasks */
.today-tasks-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    border-left: 4px solid #327881;
}

.today-tasks-list {
    margin-bottom: 1.5rem;
}

.task-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Weekly Overview */
.weekly-overview-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
}

.week-days {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.week-day {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.week-day.current {
    background: linear-gradient(135deg, #4a1e4a 0%, #5e2e60 100%);
    color: white;
    border-color: #4a1e4a;
}

.day-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.day-count {
    font-size: 1.1rem;
    color: #327881;
}

.week-day.current .day-count {
    color: white;
}

/* Today's Work View */
.today-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.today-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.today-header h2 {
    color: #5e2e60;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.today-tasks-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.cemetery-group h3 {
    color: #5e2e60;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cemetery-tasks {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.task-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.task-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.task-item.completed {
    background: #d4edda;
    border-color: #c3e6cb;
}

.task-item.in-progress {
    background: #fff3cd;
    border-color: #ffeaa7;
}

.task-item.pending {
    background: #d1ecf1;
    border-color: #bee5eb;
}

.task-status {
    font-size: 1.2rem;
    width: 30px;
    text-align: center;
}

.task-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.task-grave {
    font-weight: 600;
    color: #495057;
}

.task-time {
    font-size: 0.9rem;
    color: #6c757d;
}

/* Schedule View */
.schedule-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.schedule-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.schedule-header h2 {
    color: #5e2e60;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.schedule-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.monthly-calendar {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.calendar-weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    margin-bottom: 1rem;
}

.weekday {
    padding: 1rem;
    text-align: center;
    font-weight: 600;
    color: #5e2e60;
    background: #f8f9fa;
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: #e9ecef;
}

.calendar-day {
    background: white;
    padding: 1rem;
    min-height: 80px;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.calendar-day:hover {
    background: #f8f9fa;
}

.calendar-day.today {
    background: linear-gradient(135deg, #4a1e4a 0%, #5e2e60 100%);
    color: white;
}

.calendar-day-number {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.calendar-task-count {
    font-size: 0.8rem;
    color: #327881;
    background: #e3f2fd;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    text-align: center;
}

.calendar-day.today .calendar-task-count {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* Weekly Detail */
.weekly-detail {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.weekly-detail h3 {
    color: #5e2e60;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.week-schedule {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.day-schedule h4 {
    color: #495057;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.day-tasks {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.scheduled-task {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #e9ecef;
}

.scheduled-task.completed {
    border-left-color: #28a745;
    background: #d4edda;
}

.scheduled-task.in-progress {
    border-left-color: #ffc107;
    background: #fff3cd;
}

.scheduled-task.pending {
    border-left-color: #17a2b8;
    background: #d1ecf1;
}

.task-time {
    font-weight: 600;
    color: #495057;
    min-width: 60px;
}

.task-customer {
    flex: 1;
    color: #495057;
}

.task-status {
    font-size: 1.1rem;
}

/* Customers View */
.customers-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.customers-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.customers-header h2 {
    color: #5e2e60;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.customers-filters {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 600;
    color: #495057;
}

.filter-group input,
.filter-group select {
    padding: 0.5rem;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    font-size: 0.9rem;
}

.customers-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.customer-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: white;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.customer-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.customer-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.customer-name {
    font-weight: 600;
    font-size: 1.1rem;
    color: #495057;
}

.customer-contact {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: #6c757d;
}

.customer-details {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
}

.customer-details .paid {
    color: #28a745;
    font-weight: 600;
}

.customer-details .unpaid {
    color: #dc3545;
    font-weight: 600;
}

.customer-schedule {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: #6c757d;
}

.customer-actions {
    display: flex;
    gap: 0.5rem;
}

/* Graves View */
.graves-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.graves-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.graves-header h2 {
    color: #5e2e60;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.graves-filters {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.graves-list {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.cemetery-section h3 {
    color: #5e2e60;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.graves-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.grave-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.grave-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.grave-item.completed {
    background: #d4edda;
    border-color: #c3e6cb;
}

.grave-item.waiting {
    background: #fff3cd;
    border-color: #ffeaa7;
}

.grave-item.scheduled {
    background: #d1ecf1;
    border-color: #bee5eb;
}

.grave-item.skipped {
    background: #f8d7da;
    border-color: #f5c6cb;
}

.grave-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.grave-number {
    font-weight: 700;
    color: #495057;
    min-width: 60px;
}

.grave-customer {
    color: #6c757d;
}

.grave-status {
    font-size: 0.9rem;
    color: #495057;
}

.grave-actions {
    display: flex;
    gap: 0.5rem;
}

/* Payments View */
.payments-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.payments-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.payments-header h2 {
    color: #5e2e60;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.payments-summary {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.summary-item {
    font-size: 0.9rem;
    color: #495057;
}

.summary-item.unpaid {
    color: #dc3545;
    font-weight: 600;
}

.payments-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.payment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.payment-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.payment-item.paid {
    background: #d4edda;
    border-color: #c3e6cb;
}

.payment-item.overdue {
    background: #f8d7da;
    border-color: #f5c6cb;
}

.payment-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.payment-status {
    font-size: 1.2rem;
    width: 30px;
    text-align: center;
}

.payment-customer {
    font-weight: 600;
    color: #495057;
    min-width: 120px;
}

.payment-amount {
    font-weight: 700;
    color: #327881;
    min-width: 60px;
}

.payment-date {
    font-size: 0.9rem;
    color: #6c757d;
}

.payment-actions {
    display: flex;
    gap: 0.5rem;
}

/* Mobile Responsive Styles for Cleaning System */
@media (max-width: 768px) {
    .cleaning-navigation {
        flex-direction: column;
        gap: 1rem;
    }

    .cleaning-nav-sidebar {
        min-width: auto;
        flex-direction: row;
        overflow-x: auto;
        padding: 0.5rem;
        gap: 0.25rem;
    }

    .cleaning-nav-tab {
        white-space: nowrap;
        min-width: 120px;
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
    }

    .cleaning-nav-tab i {
        font-size: 1rem;
    }

    .overview-stats {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .week-days {
        grid-template-columns: repeat(3, 1fr);
    }

    .customers-filters,
    .graves-filters {
        flex-direction: column;
        gap: 1rem;
    }

    .customer-card,
    .grave-item,
    .payment-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .customer-contact,
    .customer-details,
    .customer-schedule {
        flex-direction: column;
        gap: 0.5rem;
    }

    .payments-summary {
        flex-direction: column;
        gap: 0.5rem;
    }

    .payment-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .task-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .schedule-controls {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .calendar-weekdays,
    .calendar-days {
        grid-template-columns: repeat(7, 1fr);
        gap: 1px;
    }

    .calendar-day {
        min-height: 60px;
        padding: 0.5rem;
        font-size: 0.8rem;
    }

    .week-schedule {
        gap: 1rem;
    }

    .scheduled-task {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

/* Extra small devices */
@media (max-width: 480px) {
    .cleaning-view {
        padding: 1rem;
    }

    .monthly-overview-card,
    .today-tasks-card,
    .weekly-overview-card {
        padding: 1rem;
    }

    .overview-stats {
        grid-template-columns: 1fr;
    }

    .week-days {
        grid-template-columns: repeat(2, 1fr);
    }

    .calendar-weekdays,
    .calendar-days {
        grid-template-columns: repeat(7, 1fr);
    }

    .calendar-day {
        min-height: 50px;
        padding: 0.25rem;
        font-size: 0.7rem;
    }

    .cleaning-nav-tab {
        min-width: 100px;
        padding: 0.5rem;
        font-size: 0.7rem;
    }
}

/* ===== PWA SPECIFIC STYLES ===== */

/* PWA mode adjustments */
.pwa-mode {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
}

/* PWA install button */
.pwa-install-button {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Offline indicator */
.offline-indicator {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: #dc3545;
    color: white;
    text-align: center;
    padding: 0.5rem;
    z-index: 9999;
    transform: translateY(-100%);
    transition: transform 0.3s ease;
}

.offline-indicator.show {
    transform: translateY(0);
}

/* PWA splash screen styles */
.pwa-splash {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #4a1e4a 0%, #5e2e60 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 10001;
    color: white;
}

.pwa-splash-logo {
    width: 120px;
    height: 120px;
    margin-bottom: 2rem;
    animation: fadeInUp 1s ease;
}

.pwa-splash-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    animation: fadeInUp 1s ease 0.2s both;
}

.pwa-splash-subtitle {
    font-size: 1rem;
    opacity: 0.8;
    animation: fadeInUp 1s ease 0.4s both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced mobile styles for PWA */
@media (max-width: 768px) {
    .pwa-mode .container {
        padding: 0.5rem;
    }

    .pwa-mode .tab-content {
        padding: 0.5rem;
    }

    .pwa-mode .cleaning-view {
        padding: 1rem;
        margin: 0.5rem;
        border-radius: 8px;
    }

    /* Touch-friendly buttons */
    .pwa-mode .btn {
        min-height: 44px;
        padding: 0.75rem 1rem;
    }

    /* Larger tap targets */
    .pwa-mode .cleaning-nav-tab {
        min-height: 48px;
        padding: 0.75rem 1rem;
    }

    /* Better spacing for mobile */
    .pwa-mode .task-item,
    .pwa-mode .customer-card,
    .pwa-mode .payment-item {
        margin-bottom: 1rem;
        padding: 1rem;
    }
}

/* Dark mode support for PWA */
@media (prefers-color-scheme: dark) {
    .pwa-mode {
        background: #1a1a1a;
        color: #ffffff;
    }

    .pwa-mode .cleaning-view {
        background: #2d2d2d;
        color: #ffffff;
    }

    .pwa-mode .cleaning-nav-sidebar {
        background: #2d2d2d;
    }

    .pwa-mode .cleaning-nav-tab {
        background: #3d3d3d;
        color: #ffffff;
        border-color: #4d4d4d;
    }

    .pwa-mode .cleaning-nav-tab:hover {
        background: #4d4d4d;
    }
}

/* Print styles for PWA */
@media print {
    .cleaning-navigation,
    .pwa-install-button,
    .camera-modal {
        display: none !important;
    }

    .cleaning-view {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* Mobile-first design for cleaning system */
.mobile-task-view {
    display: none;
}

/* Desktop hides mobile task view */
@media (min-width: 769px) {
    .mobile-task-view {
        display: none !important;
    }

    .pwa-bottom-nav {
        display: none !important;
    }
}

/* Mobile task view styles moved to main mobile block */

/* Duplicate mobile styles removed - consolidated in main mobile block */

/* Extra small devices optimization */
@media (max-width: 480px) {
    .header-content {
        padding: 0 0.5rem;
    }

    .main-nav {
        flex-direction: column;
        width: 100%;
    }

    .nav-tab {
        width: 100%;
        margin-bottom: 0.25rem;
    }

    .main-content {
        padding: 0.25rem;
    }

    /* Stack form elements vertically */
    .form-grid {
        grid-template-columns: 1fr !important;
        gap: 1rem;
    }

    /* Full-width buttons on very small screens */
    .btn, button {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        border-radius: 8px;
        margin-bottom: 0.25rem;
    }
}

/* Landscape orientation optimization */
@media (max-width: 768px) and (orientation: landscape) {
    .header {
        padding: 0.5rem 0;
    }

    .header-content {
        flex-direction: row;
        justify-content: space-between;
    }

    .main-nav {
        flex-direction: row;
        flex-wrap: nowrap;
        overflow-x: auto;
        padding-bottom: 0.5rem;
    }

    .nav-tab {
        white-space: nowrap;
        flex-shrink: 0;
    }
}

/* PWA styles moved to main mobile block */

/* ENHANCED ANIMATIONS AND INTERACTIONS */

/* Smooth transitions for all interactive elements */
* {
    transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

/* Button Animations */
.btn {
    position: relative;
    overflow: hidden;
    transform: translateZ(0);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
    transition-duration: 75ms;
}

/* Dashboard Button Animations */
.dashboard-button {
    position: relative;
    overflow: hidden;
    transform: translateZ(0);
}

.dashboard-button::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(37, 99, 235, 0.1) 0%, transparent 70%);
    transition: all 0.6s ease;
    transform: translate(-50%, -50%);
    border-radius: 50%;
}

.dashboard-button:hover::after {
    width: 300px;
    height: 300px;
}

.dashboard-button:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.dashboard-button:active {
    transform: translateY(-2px) scale(0.98);
    transition-duration: 75ms;
}

/* Card Hover Effects */
.stat-card,
.service-card,
.template-card,
.clients-stats .stat-card {
    position: relative;
    overflow: hidden;
}

.stat-card::before,
.service-card::before,
.template-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.8s ease;
}

.stat-card:hover::before,
.service-card:hover::before,
.template-card:hover::before {
    left: 100%;
}

/* Navigation Animations */
.nav-tab {
    position: relative;
    overflow: hidden;
}

.nav-tab::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-tab:hover::after {
    width: 80%;
}

.nav-tab.active::after {
    width: 100%;
    background: rgba(255, 255, 255, 1);
}

/* Service Item Animations */
.service-item {
    position: relative;
    overflow: hidden;
}

.service-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), rgba(37, 99, 235, 0.8));
    transition: width 0.3s ease;
    z-index: 0;
}

.service-item:hover::before {
    width: 4px;
}

.service-label {
    position: relative;
    z-index: 1;
}

/* Loading Animations */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

.spin {
    animation: spin 1s linear infinite;
}

/* Stagger Animation for Lists */
.service-item:nth-child(1) { animation-delay: 0.1s; }
.service-item:nth-child(2) { animation-delay: 0.2s; }
.service-item:nth-child(3) { animation-delay: 0.3s; }
.service-item:nth-child(4) { animation-delay: 0.4s; }
.service-item:nth-child(5) { animation-delay: 0.5s; }

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }

/* Touch feedback for mobile devices */
@media (hover: none) and (pointer: coarse) {
    .dashboard-button:active {
        transform: translateY(-1px) scale(0.98);
        transition: transform 0.1s ease;
    }

    .template-card:active,
    .service-card:active,
    .stat-card:active {
        transform: translateY(-1px) scale(0.98);
        transition: transform 0.1s ease;
    }

    .btn:active {
        transform: scale(0.95);
        transition: transform 0.1s ease;
    }

    .nav-tab:active,
    .mobile-menu-item:active {
        background: rgba(255, 255, 255, 0.3);
        transition: background 0.1s ease;
    }
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .dashboard-button:hover,
    .template-card:hover,
    .stat-card:hover,
    .service-card:hover {
        transform: none;
    }
}

/* Focus styles for accessibility */
.dashboard-button:focus,
.template-card:focus,
.btn:focus,
.nav-tab:focus,
.service-label:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
}

/* Ripple Effect */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Smooth Page Transitions */
.tab-pane {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.tab-pane.active {
    opacity: 1;
    transform: translateY(0);
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Micro-interactions */
.service-label:hover .service-name {
    color: var(--primary-color);
    transition: color 0.2s ease;
}

.service-label:hover .service-price {
    transform: scale(1.05);
    transition: transform 0.2s ease;
}

.checkmark:hover {
    border-color: var(--primary-color);
    transform: scale(1.1);
}

/* Smooth scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--neutral-medium);
    border-radius: 4px;
    transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* MOBILE PERFORMANCE OPTIMIZATIONS */

/* Loading Skeletons */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px;
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.skeleton-text {
    height: 1rem;
    margin-bottom: 0.5rem;
}

.skeleton-text.large {
    height: 1.5rem;
}

.skeleton-text.small {
    height: 0.75rem;
}

.skeleton-button {
    height: 2.5rem;
    width: 120px;
    border-radius: 8px;
}

.skeleton-card {
    height: 150px;
    border-radius: 12px;
    margin-bottom: 1rem;
}

.skeleton-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

/* Performance optimizations */
.dashboard-button,
.template-card,
.service-card,
.stat-card {
    will-change: transform;
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Optimize images */
img {
    max-width: 100%;
    height: auto;
    loading: lazy;
}

/* Optimize fonts */
@font-face {
    font-family: 'Inter';
    font-display: swap;
}

/* Reduce paint and layout thrashing */
.nav-tab,
.mobile-menu-item,
.btn {
    contain: layout style paint;
}

/* GPU acceleration for smooth animations */
.dashboard-button:hover,
.template-card:hover,
.stat-card:hover {
    transform: translateZ(0) translateY(-4px) scale(1.02);
}

/* Optimize touch targets for mobile */
@media (max-width: 768px) {
    .btn,
    .nav-tab,
    .dashboard-button,
    .template-card,
    .service-label {
        min-height: 44px;
        min-width: 44px;
    }

    /* Reduce animations on mobile for better performance */
    .dashboard-button::after,
    .stat-card::before,
    .service-card::before,
    .template-card::before {
        display: none;
    }

    /* Simplify hover effects on touch devices */
    .dashboard-button:hover,
    .template-card:hover,
    .stat-card:hover {
        transform: none;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
}

/* Optimize scrolling performance */
.clients-list,
.services-list,
.mobile-menu {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
}

/* Reduce motion for better performance on low-end devices */
@media (prefers-reduced-motion: reduce) {
    .skeleton {
        animation: none;
        background: #f0f0f0;
    }
}

/* Touch device optimizations */
.touch-device .touch-active {
    transform: scale(0.95);
    opacity: 0.8;
    transition: all 0.1s ease;
}

/* Lazy loading optimization */
.lazy-load {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.lazy-load.loaded {
    opacity: 1;
}

/* Critical CSS inlining optimization */
.above-fold {
    contain: layout style paint;
}

/* Preload important resources */
.preload-important {
    content-visibility: auto;
    contain-intrinsic-size: 200px;
}

/* PWA optimizations */
.pwa-optimized {
    -webkit-user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
}

/* Prevent zoom on input focus (iOS) */
input[type="text"],
input[type="email"],
input[type="tel"],
input[type="number"],
textarea,
select {
    font-size: 16px;
}

/* Optimize for high DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .dashboard-logo {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Reduce layout shifts */
.dashboard-grid,
.quick-stats,
.clients-stats {
    min-height: 200px;
}

/* Optimize for dark mode */
@media (prefers-color-scheme: dark) {
    :root {
        --neutral-light: #1f2937;
        --neutral-dark: #f9fafb;
        --neutral-medium: #d1d5db;
    }

    body {
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        color: #f9fafb;
    }

    .dashboard-button,
    .stat-card,
    .template-card,
    .service-card {
        background: #374151;
        border-color: #4b5563;
    }

    .skeleton {
        background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
        background-size: 200% 100%;
    }
}

/* Battery saving mode optimizations */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .skeleton {
        animation: none;
        background: #f0f0f0;
    }

    @media (prefers-color-scheme: dark) {
        .skeleton {
            background: #374151;
        }
    }
}

/* RESPONSIVE TESTING AND FIXES */

/* Ensure no horizontal overflow */
html, body {
    overflow-x: hidden;
    max-width: 100vw;
}

/* Fix for mobile Safari viewport units */
.mobile .dashboard-container {
    min-height: calc(100vh - 120px);
    min-height: calc(100dvh - 120px);
}

/* Prevent zoom on input focus (iOS Safari) */
@media screen and (max-width: 768px) {
    input[type="text"],
    input[type="email"],
    input[type="tel"],
    input[type="number"],
    input[type="password"],
    textarea,
    select {
        font-size: 16px !important;
        transform: translateZ(0);
    }
}

/* Fix for Android Chrome address bar */
.mobile-optimized .main-content {
    min-height: calc(100vh - 56px);
    min-height: calc(100dvh - 56px);
}

/* Disable hover effects on mobile */
.mobile-no-hover:hover {
    transform: none !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
}

/* Fix for landscape orientation on mobile */
@media screen and (max-height: 500px) and (orientation: landscape) {
    .dashboard-title {
        font-size: 1.5rem;
    }

    .dashboard-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
    }

    .dashboard-button {
        min-height: 120px;
        padding: 1rem;
    }

    .button-icon {
        font-size: 2rem;
    }

    .quick-stats {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Fix for very wide screens */
@media screen and (min-width: 1600px) {
    .dashboard-container {
        max-width: 1400px;
    }

    .quotes-layout {
        max-width: 1600px;
    }
}

/* Fix for print styles */
@media print {
    .mobile-nav-header,
    .pwa-bottom-nav,
    .mobile-menu,
    .mobile-menu-overlay {
        display: none !important;
    }

    .dashboard-button {
        break-inside: avoid;
    }
}

/* Accessibility improvements */
@media (prefers-contrast: high) {
    .dashboard-button,
    .template-card,
    .stat-card {
        border: 2px solid var(--neutral-dark);
    }

    .btn-primary {
        background: #000;
        border-color: #000;
    }
}

/* Fix for reduced data mode */
@media (prefers-reduced-data: reduce) {
    .dashboard-button::after,
    .stat-card::before,
    .template-card::before {
        display: none;
    }

    .skeleton {
        animation: none;
        background: #f0f0f0;
    }
}

/* Viewport indicator styles (development only) */
#viewport-indicator {
    pointer-events: none;
    user-select: none;
}

/* EMAIL COMPOSER MODAL */
.email-composer-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    padding: 1rem;
}

.email-composer-modal .modal-content {
    background: white;
    border-radius: 12px;
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.email-composer-modal .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    background: var(--primary-color);
    color: white;
    border-radius: 12px 12px 0 0;
}

.email-composer-modal .modal-header h3 {
    margin: 0;
    color: white;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.close-modal {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: background 0.2s ease;
}

.close-modal:hover {
    background: rgba(255, 255, 255, 0.2);
}

.email-composer-modal .modal-body {
    padding: 1.5rem;
}

.email-composer-modal .form-group {
    margin-bottom: 1.5rem;
}

.email-composer-modal .form-group label {
    display: block;
    font-weight: 500;
    color: var(--neutral-dark);
    margin-bottom: 0.5rem;
}

.email-composer-modal .form-group input,
.email-composer-modal .form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: border-color 0.2s ease;
    font-family: inherit;
}

.email-composer-modal .form-group input:focus,
.email-composer-modal .form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

/* EMAIL HISTORY */
.email-item {
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
    transition: background 0.2s ease;
}

.email-item:hover {
    background: #f8fafc;
}

.email-item:last-child {
    border-bottom: none;
}

.email-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.email-header strong {
    color: var(--neutral-dark);
    font-size: 1rem;
}

.email-date {
    color: var(--neutral-medium);
    font-size: 0.9rem;
}

.email-recipient {
    color: var(--neutral-medium);
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.email-status {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.email-status.sent {
    background: rgba(16, 185, 129, 0.1);
    color: var(--secondary-color);
}

.email-status.pending {
    background: rgba(245, 158, 11, 0.1);
    color: var(--accent-color);
}

/* NOTIFICATIONS */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 3000;
    min-width: 300px;
    max-width: 400px;
    animation: slideInRight 0.3s ease;
}

.notification.success {
    border-left: 4px solid var(--secondary-color);
}

.notification.warning {
    border-left: 4px solid var(--accent-color);
}

.notification.error {
    border-left: 4px solid #dc2626;
}

.notification.info {
    border-left: 4px solid var(--primary-color);
}

.notification-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
}

.notification-content span {
    color: var(--neutral-dark);
    font-size: 0.9rem;
    line-height: 1.4;
}

.notification-content button {
    background: none;
    border: none;
    color: var(--neutral-medium);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: background 0.2s ease;
    margin-left: 1rem;
}

.notification-content button:hover {
    background: #f3f4f6;
}

/* FIREBASE INTEGRATION STYLES */
.firebase-status {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    z-index: 1000;
    display: none;
}

.firebase-status.online {
    background: rgba(16, 185, 129, 0.9);
}

.firebase-status.offline {
    background: rgba(239, 68, 68, 0.9);
}

.firebase-status.syncing {
    background: rgba(245, 158, 11, 0.9);
}

/* Mobile optimizations for modals */
@media (max-width: 768px) {
    .email-composer-modal {
        padding: 0.5rem;
    }

    .email-composer-modal .modal-content {
        max-height: 95vh;
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions .btn {
        width: 100%;
    }

    .notification {
        right: 10px;
        left: 10px;
        min-width: auto;
        max-width: none;
    }
}

/* Mobile view specific adjustments */
.mobile-view .header-content {
    padding: 0 1rem;
}

.mobile-view .main-content {
    padding: 0.5rem;
}

.mobile-view .tab-content {
    padding: 1rem 0.5rem;
}

/* Final mobile styles moved to main mobile block */

/* Small Mobile Devices (480px and below) */
@media (max-width: 480px) {
    .dashboard-container {
        padding: 0.75rem;
    }

    .dashboard-title {
        font-size: 1.6rem;
        gap: 0.5rem;
    }

    .dashboard-logo {
        height: 32px;
    }

    .dashboard-subtitle {
        font-size: 0.9rem;
    }

    .dashboard-grid {
        gap: 0.75rem;
        margin-bottom: 1.5rem;
    }

    .dashboard-button {
        min-height: 130px;
        padding: 1rem 0.75rem;
        border-radius: 10px;
    }

    .button-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .button-content h3 {
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
    }

    .button-content p {
        font-size: 0.7rem;
        line-height: 1.2;
    }

    .quick-stats {
        gap: 0.75rem;
    }

    .stat-card {
        padding: 0.75rem;
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .stat-icon {
        font-size: 1.5rem;
    }

    .stat-number {
        font-size: 1.4rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    /* Email mobile optimization */
    .email-layout {
        padding: 0.75rem;
    }

    .template-card {
        padding: 1rem;
    }

    .template-card i {
        font-size: 1.75rem;
        margin-bottom: 0.75rem;
    }

    .template-card h4 {
        font-size: 1rem;
    }

    .template-card p {
        font-size: 0.8rem;
    }
}

/* Very small screens (iPhone SE, etc.) */
@media (max-width: 375px) {
    .header-content {
        padding: 0 0.5rem;
    }

    .main-content {
        padding: 0.25rem;
    }

    .dashboard-container {
        padding: 0.5rem;
    }

    .dashboard-title {
        font-size: 1.4rem;
    }

    .dashboard-grid {
        gap: 0.5rem;
    }

    .dashboard-button {
        min-height: 120px;
        padding: 0.75rem 0.5rem;
    }

    .button-icon {
        font-size: 1.75rem;
    }

    .button-content h3 {
        font-size: 0.85rem;
    }

    .button-content p {
        font-size: 0.65rem;
    }

    .pwa-bottom-nav .nav-item {
        font-size: 0.6rem;
        min-width: 50px;
        padding: 0.4rem;
    }

    .pwa-bottom-nav .nav-item i {
        font-size: 1rem;
        margin-bottom: 0.2rem;
    }
}

/* Landscape orientation on mobile */
@media (max-height: 500px) and (orientation: landscape) {
    .header {
        padding: 0.25rem 0;
    }

    .pwa-bottom-nav {
        padding: 0.25rem;
    }

    .pwa-bottom-nav .nav-item {
        padding: 0.25rem;
    }
}
